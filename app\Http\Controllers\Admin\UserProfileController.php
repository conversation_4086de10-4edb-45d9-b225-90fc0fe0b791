<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\Client;
use App\Models\Wallet;
use App\Models\Advance;
use App\Models\Setting;
use App\Models\Document;
use App\Constants\Status;
use App\Models\WorkerDue;
use App\Constants\UserType;
use App\Models\DocumentType;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\CustomShipping;
use App\Models\ShippingInvoice;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Constants\CustomShippingStatus;
use App\Constants\ShippingInvoiceStatus;
use Yajra\DataTables\Facades\DataTables;

class UserProfileController extends Controller
{
    public function index($id)
    {
        try {
            $data["title"] = __("User");
            $data["sub_title"] = __("User Profile");
            $user = User::findOrFail($id);
            $user = $user->load("avatar");
            $data["user"] = $user;
            $data["total_advances"] = Advance::where("to_user_id", $id)->sum("amount");
            $data["total_due"] = WorkerDue::where("user_id", $id)->first()->amount ?? 0;
            if ($user->type == UserType::Broker) {
                $data["custom_shippings_count"] = CustomShipping::where("assigned_to", $id)->count();
                $data["active_custom_shippings_count"] = CustomShipping::where("assigned_to", $id)
                    ->where("status", "<=", CustomShippingStatus::RELEASED)
                    ->count();
            } else {
                $data["custom_shippings_count"] = CustomShipping::where("created_by", $id)->count();
                $data["active_custom_shippings_count"] = CustomShipping::where("created_by", $id)
                    ->where("status", "<=", CustomShippingStatus::RELEASED)
                    ->count();
            }
            $invoices = ShippingInvoice::where("user_id", $id)->with("shippingExpenses")->get();
            $data["total_invoices_amount"] = $invoices->sum(fn($el) => $el->total_amount);
            $data["id"] = $id;
            $data["worker_due"] = WorkerDue::where("user_id", $id)->first();
            $data["wallet_balance"] = Wallet::where("user_id", $id)->value("amount");
            if ($user->type == UserType::Broker) {
                return view('admin.users.broker_profile', $data);
            } else if ($user->type == UserType::SystemEmployee) {
                return view('admin.users.employee_profile', $data);
            }
        } catch (Exception $ex) {
            // return $ex;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    public function loadAdvances($id)
    {
        $advances = Advance::query()->where("to_user_id", $id);

        return DataTables::of($advances)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "status", "attachments"])
            ->addColumn('action', function ($advance) {
                return '
                    <a href="' . route("admin.advances.edit", $advance->id) . '" class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap"><i class="fas fa-edit"></i></a>
                    <a href="javascript:;" onclick="deleteRecord(' . $advance->id . ')" class="btn btn-sm btn-danger waves-effect waves-light text-nowrap"><i class="fas fa-trash"></i></a>
                ';
            })->addColumn("attachments", function ($advance) {
                return view("admin.users.partials.advances_attachments", ["item" => $advance]);
            })
            ->addColumn("status", function ($item) {
                $output = "";
                if ($item->status && Status::Approved->equals($item->status)) {
                    $output = ' <span class="badge bg-success">' . __($item->status->label()) . '</span>';
                } else if ($item->status && Status::Pending->equals($item->status)) {
                    $output = '<span class="badge bg-warning">' .  __($item->status->label()) . '</span>';
                } elseif ($item->status && Status::Rejected->equals($item->status)) {
                    $output =  '<span class="badge bg-danger">' . __($item->status->label()) . '</span>';
                } else {
                    $output =  '<span>-</span>';
                }
                return $output;
            })
            ->make(true);
    }
    public function loadDocuments($id)
    {
        $documents = DocumentType::withCount("documents")
            ->whereHas("users", function ($inner) use ($id) {
                $inner->where("users.id", $id);
            });
        return DataTables::of($documents)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "documents_count", "document_type"])
            ->addColumn('action', function ($item) {
                return '<a href="' . route('admin.documents.typesPage', $item->id) .  '"class="text-info"><i class="fas fa-eye"></i></a>';
            })
            ->addColumn("document_type", function ($item) {
                return '<a href="' . route('admin.documents.typesPage', $item->id) . '">' . $item->name . '</a>';
            })
            ->addColumn("documents_count", function ($item) {

                return $item->documents_count;
            })
            ->make(true);
    }
    public function loadAllDocuments($id)
    {
        $documents = Document::with("documentType")
            ->whereHas("documentType.users", function ($inner) use ($id) {
                $inner->where("users.id", $id);
            });
        return DataTables::of($documents)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "attachments", "status", "document_type"])
            ->addColumn('action', function ($document) {
                return '
                    <a href="' . route("admin.documents.edit", $document->id) . '" class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap"><i class="fas fa-edit"></i></a>
                    <a href="javascript:;" onclick="deleteDocument(' . $document->id . ')" class="btn btn-sm btn-danger waves-effect waves-light text-nowrap"><i class="fas fa-trash"></i></a>
                ';
            })
            ->addColumn("attachments", function ($advance) {
                return view("admin.users.partials.documents_attachments", ["item" => $advance]);
            })
            ->addColumn("document_type", function ($item) {
                return $item->documentType->name;
            })
            ->addColumn("status", function ($item) {

                return $item->remaining_days;
            })
            ->make(true);
    }
    public function loadInvoices($id)
    {
        $invoices = ShippingInvoice::query()->with("customShipping")->withCount("shippingExpenses")->where("user_id", $id);
        return DataTables::of($invoices)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "status", "attachments"])
            ->addColumn('action', function ($invoice) {
                return view("admin.users.partials.shipping_invoices_btn", ["item" => $invoice]);
            })->addColumn("attachments", function ($invoice) {
                return view("admin.users.partials.shipping_invoices_attachments", ["item" => $invoice]);
            })
            ->editColumn("status", function ($invoice) {
                if ($invoice->status == ShippingInvoiceStatus::Pending) {
                    return "<span class='badge bg-warning'>" . __("Pending") . "</span>";
                } else if ($invoice->status == ShippingInvoiceStatus::Approved) {
                    return "<span class='badge bg-success'>" . __("Approved") . "</span>";
                } else if ($invoice->status == ShippingInvoiceStatus::Rejected) {
                    return "<span class='badge bg-danger'>" . __("Rejected") . "</span>";
                } else {
                    return "-";
                }
            })->addColumn("shipping_expenses_count", function ($invoice) {
                return @$invoice->shipping_expenses_count;
            })->addColumn("policy_number", function ($invoice) {
                return @$invoice->customShipping->policy_number;
            })->addColumn("code", function ($invoice) {
                return @$invoice->customShipping->code;
            })
            ->addColumn("packages_count", function ($invoice) {
                return @$invoice->customShipping->packages_count;
            })
            ->addColumn("company_name", function ($invoice) {
                return @$invoice->customShipping->company->name;
            })
            ->addColumn("from_country", function ($invoice) {
                return @$invoice->customShipping->fromCountry->name;
            })
            ->addColumn("total_amount", function ($invoice) {
                return number_format(@$invoice->total_amount, 2);
            })->editColumn("created_at", function ($invoice) {
                return $invoice->created_at->toDateString();
            })
            ->make(true);
    }

    public function loadCustomShippings($id)
    {
        $user = User::find($id);
        if ($user->type == UserType::Broker) {
            $customShippings = CustomShipping::where("assigned_to", $id)->get();
        } else if ($user->type == UserType::SystemEmployee) {
            $customShippings = CustomShipping::where("created_by", $id)->get();
        }

        return DataTables::of($customShippings)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "status"])
            ->addColumn('action', function ($invoice) {
                return view("admin.users.partials.custom_shippings_btn", ["item" => $invoice]);
            })->editColumn("status", function ($invoice) {
                if ($invoice->status == CustomShippingStatus::ASSIGNED) {
                    return "<span class='badge bg-danger'>" . __("Assigned") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::FULL_DELIVERED) {
                    return "<span class='badge bg-primary'>" . __("Full Delivered") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::ThroughShipping) {
                    return "<span class='badge bg-dark'>" . __($invoice->status->label()) . "</span>";
                } else if ($invoice->status == CustomShippingStatus::PARTIAL_DELIVERED) {
                    return "<span class='badge bg-info'>" . __("Partial Delivered") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::INSPECTED) {
                    return "<span class='badge bg-warning'>" . __("Inspected") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::RELEASED) {
                    return "<span class='badge bg-success'>" . __("Released") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::Distributed) {
                    return "<span class='badge bg-secondary'>" . __($invoice->status->label()) . "</span>";
                } else {
                    return "-";
                }
            })
            ->addColumn("company_name", function ($invoice) {
                return @$invoice->company->name;
            })
            ->addColumn("from_country", function ($invoice) {
                return @$invoice->fromCountry->name;
            })
            ->editColumn("created_at", function ($shipping) {
                return $shipping->created_at->toDateString();
            })
            ->make(true);
    }

    public function exportAdvancesToPdf($id)
    {
        $user = User::findOrFail($id);
        $logo = Setting::where('key', 'logo')->first();
        $advances = Advance::where("to_user_id", $id)->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.users.exports.export_advances', [
            'advances' => $advances,
            'logo' => $logo,
            'user' => $user
        ]);

        $pdf->Output(time() . "_advances.pdf", 'D');
    }
    public function exportCustomShippingsToPdf($id)
    {
        $user = User::findOrFail($id);
        $logo = Setting::where('key', 'logo')->first();
        $shippings = CustomShipping::with("assignedTo")->where("assigned_to", $id)->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.users.exports.export_custom_shippings', [
            'shippings' => $shippings,
            'logo' => $logo,
            'user' => $user
        ]);

        $pdf->Output(time() . "_shippings.pdf", 'D');
    }
}
