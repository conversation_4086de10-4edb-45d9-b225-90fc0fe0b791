<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Constants\CustomShippingStatus as CustomShippingStatusEnum;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class CustomShippingStatus extends Model
{
    protected $fillable = [
        "custom_shipping_id",
        "status",
        "packages_count",
        "description",
        "date",
        "created_by",
        "updated_by"
    ];

    protected $casts = [
        "status" => CustomShippingStatusEnum::class
    ];
    public function customShipping(): BelongsTo
    {
        return $this->belongsTo(CustomShipping::class);
    }

    public function attachment(): MorphOne
    {
        return $this->morphOne(Attachment::class, "attachmentable");
    }

    public function statusColor(): Attribute
    {
        $color = match ($this->status) {
            CustomShippingStatusEnum::ASSIGNED => "info",
            CustomShippingStatusEnum::BillOfLadingProcessing => "info",
            CustomShippingStatusEnum::ShipmentNotReceivedDelayed => "danger",
            CustomShippingStatusEnum::BillOfLadingAknowledged => "success",
            CustomShippingStatusEnum::ThroughShipping => "warning",
            CustomShippingStatusEnum::Expo_ThroughShipping => "warning",
            CustomShippingStatusEnum::PARTIAL_DELIVERED => "warning",
            CustomShippingStatusEnum::FULL_DELIVERED => "success",
            CustomShippingStatusEnum::Expo_Received => "success",
            CustomShippingStatusEnum::CustomsClearanceInitiated => "info",
            CustomShippingStatusEnum::Expo_ClearanceInitiated => "info",
            CustomShippingStatusEnum::CustomsClearanceCompleted => "success",
            CustomShippingStatusEnum::Expo_ClearanceCompleted => "success",
            CustomShippingStatusEnum::UnderCustomsExamination => "warning",
            CustomShippingStatusEnum::INSPECTED => "success",
            CustomShippingStatusEnum::ShipmentInTransitFromCustoms => "primary",
            CustomShippingStatusEnum::Expo_ThroughExporting => "primary",
            CustomShippingStatusEnum::RELEASED => "success",
            CustomShippingStatusEnum::Distributed => "success",
            CustomShippingStatusEnum::Expo_Exported => "success",
        };
        return Attribute::make(get: fn() => $color);
    }
}
