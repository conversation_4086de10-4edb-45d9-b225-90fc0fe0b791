<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advances', function (Blueprint $table) {
            $table->id();
            $table->foreignId("to_user_id")->nullable()->constrained("users")->onDelete("cascade");
            $table->foreignId("from_user_id")->nullable()->constrained("users")->onDelete("cascade");

            $table->decimal("amount", 10, 2)->default(0.00);
            $table->date("date")->nullable();
            $table->integer("status")->nullable();
            $table->text("notes")->nullable();
            $table->decimal("current_balance", 10, 2)->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advances');
    }
};
