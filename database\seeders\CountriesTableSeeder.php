<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Constants\CountryType;
use App\Models\AirPort;
use App\Models\Harbor;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class CountriesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // if (Country::count() == 0) {
        DB::table("countries")->delete();

        // Country::insert([
        //     ['short_name' => 'KW', 'name' => 'Kuwait', 'iso3' => 'KWT', 'number_code' => '414', 'phone_code' => '965', 'is_default' => 1, 'type' => CountryType::IMPORTING],
        //     ['short_name' => 'CN', 'name' => 'China', 'iso3' => 'CHN', 'number_code' => '156', 'phone_code' => '86', 'is_default' => 0, 'type' => CountryType::EXPORTING],
        //     ['short_name' => 'TR', 'name' => 'Turkey', 'iso3' => 'TUR', 'number_code' => '792', 'phone_code' => '90', 'is_default' => 0, "type" => CountryType::BOTH],
        // ]);


        $sheet_path = public_path("uploads/new_countries_airports.xlsx");
        $rows = Excel::toCollection(null, $sheet_path)->first();

        $last_country_id = null; // To keep track of the last created country's ID

        foreach ($rows as $key => $row) {
            // Skip header row
            if ($key === 0) continue;

            // Check if the row has a country name
            if (!empty($row[0])) {
                // Create a new country and update the last country ID
                $country = Country::create([
                    "name" => $row[0],
                    "name_ar" => $row[0],
                    "name_en" => $row[1],
                    "short_name" => $row[2],
                    "iso3" => $row[2],
                    "number_code" => $row[3],
                    "phone_code" => $row[3],
                    "is_default" => $row[1] == "Kuwait" ? true : false
                ]);

                $last_country_id = $country->id; // Update the last country ID
            }

            // Use the last_country_id for airports and harbors
            if (!empty($row[4]) && $last_country_id !== null && $row[4] != "-") {
                AirPort::create([
                    "name" => $row[4],
                    "name_ar" => $row[4],
                    "name_en" => $row[5],
                    "code" => $row[6],
                    "country_id" => $last_country_id
                    // Uncomment the line below if the code column exists
                    // "code" => $row[5]
                ]);
            }

            if (!empty($row[7]) && $last_country_id !== null && $row[7] != "-") {
                Harbor::create([
                    "name" => $row[7],
                    "name_ar" => $row[7],
                    "name_en" => $row[8],
                    "code" => $row[9],
                    "country_id" => $last_country_id
                    // Uncomment the line below if the code column exists
                    // "code" => $row[7]
                ]);
            }
        }
        // }
    }
}
