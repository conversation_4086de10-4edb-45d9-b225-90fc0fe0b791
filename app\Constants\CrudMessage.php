<?php

namespace App\Constants;

class CrudMessage
{
    public const CreatedSuccessfully = "data is added successfully";
    public const UpdatedSuccessfully = "data is updated successfully";
    public const DeletedSuccessfully = "data is deleted successfully";
    public const DataNotFound = "data not found";
    public const SomethingWentWrong = "something went wrong, please try again";

    public static function isRequired($name)
    {
        return $name . " " . __("is required");
    }
    public static function minLengthCharacters($min)
    {
        return  __("minimum allowed length is") . " " . $min . __("characters");
    }
    public static function maxLengthCharacters($max)
    {
        return  __("maximum allowed length is") . " " . $max . __("characters");
    }
    public static function alreadyExists($name)
    {
        return $name . " " . __("already exists");
    }
    public static function isNotValid($name)
    {
        return $name . " " . __("is not valid");
    }
}
