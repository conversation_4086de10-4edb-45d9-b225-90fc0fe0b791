<?php

namespace App\Constants;

enum Status: int
{
    case Pending = 1;
    case Approved = 2;
    case Rejected = 3;

    public static function availableTypes(): array
    {
        return  [
            "Pending" => self::Pending,
            "Approved" => self::Approved,
            "Rejected" => self::Rejected,
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::Pending => "Pending",
            self::Approved => "Approved",
            self::Rejected => "Rejected",
        };
    }
    /**
     * Compare the enum with a value.
     *
     * @param int|Status $value
     * @return bool
     */
    public function equals(int|Status $value): bool
    {
        return $value instanceof self ? $this === $value : $this->value === $value;
    }
}
