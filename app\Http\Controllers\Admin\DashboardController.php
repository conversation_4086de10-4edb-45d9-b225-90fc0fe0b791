<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Client;
use App\Models\Advance;
use App\Models\Expense;
use App\Models\Revenue;
use App\Models\Purchase;
use App\Models\WorkerDue;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Http\Request;
use App\Models\CustomShipping;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Constants\CustomShippingStatus;

class DashboardController extends Controller
{
    public function index()
    {
        $data = [];
        $data["title"] = __("Dashboard");
        $data["route"] = null;
        if (auth("web")->user()->IsSuperAdmin) {
            $this->loadAdmin($data);
        } else if (!auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::Broker) {
            $this->loadBroker($data);
            $data["route"] = route("admin.accountStatement.exportBroker", ['user_id' => auth("web")->id(), 'lang' => app()->getLocale()]);
        } else if (!auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::SystemEmployee) {
            $this->loadSystemEmployee($data);
        } else if (!auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::Client) {
            $this->loadclient($data);
            $data["route"] = route("admin.accountStatement.export", ['user_id' => auth("web")->id(), 'lang' => app()->getLocale()]);
        } else if (!auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::SubClient) {
            $this->loadSubclient($data);
            $client = Client::findOrFail(auth("web")->user()->client_id);
            $data["route"] = route("admin.accountStatement.export", ['user_id' => $client->user_id, 'lang' => app()->getLocale()]);
        }
        return view("admin.dashboard.index", $data);
    }

    public function loadAdmin(&$data)
    {
        $data["total_clients"] = Client::count();
        $data["total_brokers"] = User::where("type", "=", UserType::Broker->value)->where("IsSuperAdmin", "!=", 1)->count();
        $data["total_custom_shippings"] = CustomShipping::count();
        $data["total_inspected_custom_shippings"] = CustomShipping::where("status", CustomShippingStatus::INSPECTED->value)->count();
        $data["total_released_custom_shippings"] = CustomShipping::where("status", CustomShippingStatus::RELEASED->value)->count();
        $data["total_delivered_custom_shippings"] = CustomShipping::where("status", CustomShippingStatus::Distributed->value)->count();

        $data["total_unfinished_custom_shippings"] = CustomShipping::whereNotIn("status", [
            CustomShippingStatus::Distributed->value,
            CustomShippingStatus::Expo_Exported->value,
            CustomShippingStatus::RELEASED->value
        ])->count();

        $data["total_delayed_custom_shippings"] = CustomShipping::where("status", CustomShippingStatus::ShipmentNotReceivedDelayed->value)->count();

        $data["total_workers_dues"] =  WorkerDue::sum("amount");
        $data["total_clients_debits"] =  ClientDebit::sum("amount");

        $data["total_advances_to_workers"] = Advance::where("to_user_id", "!=", null)->sum("amount");
        $data["total_advances_from_clients"] = Advance::where("from_user_id", "!=", null)->sum("amount");

        $data["total_revenues"] = Revenue::query()->sum("amount");
        $data["total_purchases"] = Purchase::query()->sum("amount");
        $data["total_expenses"] = Expense::query()->sum("amount");
        return $data;
    }
    public function loadBroker(&$data)
    {
        $data["total_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->count();

        $data["total_inspected_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->where("status", CustomShippingStatus::INSPECTED->value)->count();

        $data["total_released_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->where("status", CustomShippingStatus::RELEASED->value)->count();

        $data["total_unfinished_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->whereNotIn("status", [
            CustomShippingStatus::Distributed->value,
            CustomShippingStatus::Expo_Exported->value,
            CustomShippingStatus::RELEASED->value
        ])->count();

        $data["total_delivered_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->where("status", CustomShippingStatus::Distributed->value)->count();

        $data["total_workers_dues"] =  WorkerDue::where("user_id", auth("web")->id())->sum("amount");
        $data["total_advances_to_workers"] = Advance::where("to_user_id", auth("web")->id())->sum("amount");
    }
    public function loadSystemEmployee(&$data)
    {
        $data["total_custom_shippings"] = CustomShipping::where("created_by", auth("web")->id())->count();

        $data["total_inspected_custom_shippings"] = CustomShipping::where("created_by", auth("web")->id())->where("status", CustomShippingStatus::INSPECTED->value)->count();

        $data["total_released_custom_shippings"] = CustomShipping::where("created_by", auth("web")->id())->where("status", CustomShippingStatus::RELEASED->value)->count();

        $data["total_delivered_custom_shippings"] = CustomShipping::where(function ($query) {
            $query->where("assigned_to", auth("web")->id())
                ->orWhere('created_by', auth("web")->id());
        })->where("status", CustomShippingStatus::Distributed->value)->count();

        $data["total_unfinished_custom_shippings"] = CustomShipping::where("created_by", auth("web")->id())->whereNotIn("status", [
            CustomShippingStatus::Distributed->value,
            CustomShippingStatus::Expo_Exported->value,
            CustomShippingStatus::RELEASED->value
        ])->count();
    }
    public function loadclient(&$data)
    {
        $client = Client::where("user_id", auth("web")->id())->first();
        $data["total_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->count();

        $data["total_inspected_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::INSPECTED->value)->count();

        $data["total_released_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::RELEASED->value)->count();

        $data["total_delivered_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::Distributed->value)->count();

        $data["total_unfinished_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->whereNotIn("status", [
            CustomShippingStatus::Distributed->value,
            CustomShippingStatus::Expo_Exported->value,
            CustomShippingStatus::RELEASED->value
        ])->count();

        $data["total_clients_debits"] =  ClientDebit::where("user_id", auth("web")->id())->sum("amount");
        $data["total_advances_from_clients"] = Advance::where("from_user_id", auth("web")->id())->sum("amount");
    }
    public function loadSubclient(&$data)
    {
        $client = Client::where("id", auth("web")->user()->client_id)->first();
        $data["total_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->count();

        $data["total_inspected_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::INSPECTED->value)->count();

        $data["total_released_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::RELEASED->value)->count();

        $data["total_delivered_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->where("status", CustomShippingStatus::Distributed->value)->count();

        $data["total_unfinished_custom_shippings"] = CustomShipping::where(function ($query) use ($client) {
            $query->where("client_id", $client->id)
                ->orWhere('created_by', auth('web')->id());
        })->whereNotIn("status", [
            CustomShippingStatus::Distributed->value,
            CustomShippingStatus::Expo_Exported->value,
            CustomShippingStatus::RELEASED->value
        ])->count();

        $data["total_clients_debits"] =  ClientDebit::where("user_id", $client->user_id)->sum("amount");
        $data["total_advances_from_clients"] = Advance::where("from_user_id", $client->user_id)->sum("amount");
    }
}
