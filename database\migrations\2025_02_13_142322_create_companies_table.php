<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string("name")->nullable();
            $table->string("name_ar")->nullable();
            $table->string("name_en")->nullable();
            $table->foreignId("client_id")->nullable()->constrained("clients")->cascadeOnDelete();
            $table->text("notes")->nullable();
            $table->timestamps();
        });

        Schema::table("users",  function (Blueprint $table) {
            $table->foreignId("client_id")->nullable()->constrained("clients")->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
