<?php

namespace App\Http\Requests\Admin\Advance;

use App\Constants\UserType;
use App\Constants\CrudMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdateClientDebitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "due_id" => "required",
            "amount" => "required",
            "date" => "nullable",
            "action_type" => "required",
            "status" => "required",
            "notes" => "nullable",
            "updated_by" => "sometimes",
            "attachments" => "nullable|array"
        ];
    }


    public function messages(): array
    {
        return [
            "due_id.required" => CrudMessage::isRequired(__("Item")),
            "action_type.required" => CrudMessage::isRequired(__("Action Type")),
            "status.required" => CrudMessage::isRequired(__("Status")),
            "amount.required" => CrudMessage::isRequired(__("amount")),
        ];
    }
    protected function prepareForValidation()
    {
        $this->merge([
            "updated_by" => auth("web")->id()
        ]);
    }
}
