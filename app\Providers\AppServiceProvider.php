<?php

namespace App\Providers;

use Illuminate\Foundation\AliasLoader;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Get the AliasLoader instance
        $loader = AliasLoader::getInstance();

        // Add your aliases
        $loader->alias('CustomShippingType', \App\Constants\CustomShippingType::class);
        $loader->alias('CustomShippingStatus', \App\Constants\CustomShippingStatus::class);
        $loader->alias('ShippingInvoiceStatus', \App\Constants\ShippingInvoiceStatus::class);
        $loader->alias('WorkerDueAction', \App\Constants\WorkerDueAction::class);
        $loader->alias('CrudMessage', \App\Constants\CrudMessage::class);
        $loader->alias('AttachmentVisibleStatus', \App\Constants\AttachmentVisibleStatus::class);
        $loader->alias('UserType', \App\Constants\UserType::class);
        $loader->alias('DeliveryMethod', \App\Constants\DeliveryMethod::class);
        $loader->alias('Status', \App\Constants\Status::class);
        $loader->alias('FinancialMonthStatus', \App\Constants\FinancialMonthStatus::class);
    }

    /**p
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
