<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Wallet;
use App\Models\Revenue;
use App\Models\Purchase;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Events\NewRevenueAddedEvent;
use App\Constants\AttachmentVisibleStatus;
use App\Models\ClientDebit;

class RevenueService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            $revenue = Revenue::create($data);
            if (isset($data["client_id"])) {
                $client = Client::findOrFail($data["client_id"]);
                $wallet = Wallet::where("user_id", "=", $client->user_id)->first();
                if (isset($wallet)) {
                    $revenue->current_balance = $wallet->amount;
                    $revenue->save();
                }

                $clientDebit = ClientDebit::where(['user_id' => $client->user_id])->first();
                if (isset($clientDebit)) {
                    $clientDebit->amount += $data["amount"];
                    $clientDebit->save();
                }
            }
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/revenues/$revenue->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $revenue->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            NewRevenueAddedEvent::dispatch($revenue->amount);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $revenue = Revenue::findOrFail($id);

            $new_amount = $data["amount"];
            $amount_diff = $new_amount -  $revenue->amount;

            $revenue->update($data);
            if (isset($data["client_id"])) {
                $client = Client::findOrFail($data["client_id"]);
                $wallet = Wallet::where("user_id", "=", $client->user_id)->first();
                if (isset($wallet) && $amount_diff > 0) {
                    $revenue->current_balance = $wallet->amount;
                    $revenue->save();
                }
                $clientDebit = ClientDebit::where(['user_id' => $client->user_id])->first();
                if (isset($clientDebit)) {
                    $clientDebit->amount += $amount_diff;
                    $clientDebit->save();
                }
            }
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/revenues/$revenue->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $revenue->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            NewRevenueAddedEvent::dispatch($amount_diff);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
