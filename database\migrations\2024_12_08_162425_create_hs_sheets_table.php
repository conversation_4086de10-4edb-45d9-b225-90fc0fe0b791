<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hs_sheets', function (Blueprint $table) {
            $table->id();
            $table->string("section_code")->nullable();
            $table->text("section_name_ar")->nullable();
            $table->text("section_name_en")->nullable();

            $table->string("chapter_code")->nullable();
            $table->text("chapter_name_ar")->nullable();
            $table->text("chapter_name_en")->nullable();

            $table->string("heading_code")->nullable();
            $table->text("heading_name_ar")->nullable();
            $table->text("heading_name_en")->nullable();

            $table->string("hs_code")->nullable();
            $table->string("duty")->nullable();
            $table->text("name_en")->nullable();
            $table->text("name_ar")->nullable();
            $table->text("description")->nullable();
            $table->string("unit")->nullable();

            $table->json("restriction_import")->nullable();
            $table->json("prohibition_import")->nullable();

            $table->json("restriction_export")->nullable();
            $table->json("prohibition_export")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hs_sheets');
    }
};
