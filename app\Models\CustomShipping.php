<?php

namespace App\Models;

use App\Constants\UserType;
use App\Models\Traits\Auditable;
use App\Constants\DeliveryMethod;
use App\Constants\CustomShippingType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use App\Constants\CustomShippingStatus as CustomShippingStatusEnum;

class CustomShipping extends Model
{
    use Auditable;
    protected $fillable = [
        "policy_number",
        "code",
        "type",
        "shipping_type",
        "client_id",
        "company_id",
        "cliet_name",
        "packages_count",
        "remained_packages_count",
        "delivered_packages_count",
        "units_count",
        "total_weight",
        "total_size",
        "assigned_to",
        "country_id",
        "from_country_id",
        "date",
        "delivery_method",
        "air_port_id",
        "harbor_id",
        "has_subsequents",
        "derived_from",
        "invoice_date",
        "created_by",
        "updated_by",
        "status"
    ];

    protected $casts = [
        "status" => CustomShippingStatusEnum::class,
        "type" => CustomShippingType::class,
        "shipping_type" => CustomShippingType::class,
        "delivery_method" => DeliveryMethod::class,
    ];
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, "assigned_to");
    }
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, "client_id");
    }
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, "company_id");
    }
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, "attachmentable");
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }

    public function shippingStatus(): HasMany
    {
        return $this->hasMany(CustomShippingStatus::class);
    }
    public function shippingInvoice(): HasOne
    {
        return $this->hasOne(ShippingInvoice::class);
    }
    public function shippingInvoices(): HasMany
    {
        return $this->hasMany(ShippingInvoice::class);
    }

    public function derivedFrom(): BelongsTo
    {
        return $this->belongsTo(CustomShipping::class, "derived_from");
    }
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
    public function fromCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, "from_country_id");
    }
    public function airport(): BelongsTo
    {
        return $this->belongsTo(AirPort::class, "air_port_id");
    }
    public function harbor(): BelongsTo
    {
        return $this->belongsTo(Harbor::class);
    }
    public function statusStr(): Attribute
    {
        return Attribute::make(get: fn() => $this->status != null ? $this->status->label() : null);
    }
    public function typeStr(): Attribute
    {
        return Attribute::make(get: fn() => $this->type != null ? $this->type->label() : null);
    }

    public function employeeInvoiceId(): Attribute
    {
        $invoice =  $this->shippingInvoices()->whereHas("user", function ($query) {
            $query->where("type", UserType::Broker->value);
        })->first(["id"]);
        return Attribute::make(get: fn() => @$invoice->id);
    }
    public function clientInvoiceId(): Attribute
    {
        $invoice =  $this->shippingInvoices()->whereHas("user", function ($query) {
            $query->where("type", UserType::Client->value);
        })->first(["id"]);
        return Attribute::make(get: fn() => @$invoice->id);
    }

    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.custom_shippings.edit");
    }
}
