<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("attachmentable_id")->nullable();
            $table->string("attachmentable_type")->nullable();
            $table->string("file_path")->nullable();
            $table->string("type")->nullable();
            $table->string("mime_type")->nullable();
            $table->string("filename")->nullable();
            $table->string("original_filename")->nullable();
            $table->integer("visible_to")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachments');
    }
};
