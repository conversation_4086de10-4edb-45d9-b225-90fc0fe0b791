<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Models\HsSheet;

class HsSheetController extends Controller
{
    public function index()
    {
        $data["title"] = __("Hs List");
        $data["code"] = request()->query("code");
        $data["description"] = request()->query("description");
        // return $data;
        return view("admin.hs_sheet.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $Code = null;
            $Name = null;

            if (isset($request->Code)) {
                $Code = trim(Str::lower($request->Code));
            }
            if (isset($request->Name)) {
                $Name = trim(Str::lower($request->Name));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = HsSheet::when($Code, function ($query) use ($Code) {
                $query->where("hs_code", "LIKE", "%" . $Code . "%");
            })->when($Name, function ($query) use ($Name) {
                $query->where("name_ar", "LIKE", "%" . $Name . "%")
                    ->orWhere("name_en", "LIKE", "%" . $Name . "%");
            });
            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.hs_sheet.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    public function show($id)
    {
        $data["title"] = __("Hs Details");
        $data["hs_sheet"] = HsSheet::findOrFail($id);
        // return $data;
        return view("admin.hs_sheet.details", $data);
    }

    public function exportDetailsToPdf($id)
    {
        $logo = "";
        $hs_sheet = HsSheet::findOrFail($id);
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.hs_sheet.details_pdf', [
            'hs_sheet' => $hs_sheet,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_hs_sheet.pdf", 'D');
    }
}
