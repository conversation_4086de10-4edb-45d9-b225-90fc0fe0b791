<?php

namespace App\Services;

use Exception;
use App\Models\User;
use App\Models\Attachment;
use Illuminate\Support\Str;
use App\Models\CustomShipping;
use App\Models\ShippingInvoice;
use App\Constants\DeliveryMethod;
use Illuminate\Support\Facades\DB;
use App\Models\CustomShippingStatus;
use App\Constants\CustomShippingType;
use App\Constants\ShippingInvoiceStatus;
use App\Services\ShippingInvoiceService;
use App\Constants\AttachmentVisibleStatus;
use App\Constants\CustomShippingStatus as CustomShippingStatusEnum;

class CustomShippingService
{
    public function __construct(protected ShippingInvoiceService $shippingInvoiceService) {}
    public function updateStatus(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $customShipping = CustomShipping::findOrFail($id);
            $old_status = $customShipping->status;
            $status = $data["status"];
            $invoice_date = $data["invoice_date"] ?? null;
            if ($invoice_date) {
                $customShipping->invoice_date = $invoice_date;
            }
            if ($status != CustomShippingStatusEnum::PARTIAL_DELIVERED->value && $status == $customShipping->status->value) {
                throw new Exception(__("nothing has be changed in the shipping status"));
            }
            if ($customShipping->status->value > $status) {
                //delete the after
                // CustomShippingStatus::where("custom_shipping_id", $id)->where('status', ">", $status)->delete();
            }

            if ($status == CustomShippingStatusEnum::PARTIAL_DELIVERED->value) {

                if ($data["packages_count"] > $customShipping->remained_packages_count) {
                    throw new Exception(__("packages count unavailable"));
                }
                $customShipping->remained_packages_count -=  $data["packages_count"];
                $customShipping->has_subsequents = true;
                //create new custom shipping
                if ($customShipping->remained_packages_count > 0) {
                    $newShipping = $this->createNewCustomShipping($id, $customShipping->remained_packages_count);
                    $this->createNotificationAfterShippingPartiallyDelivered($newShipping);
                }
            }


            $shippingStatus = CustomShippingStatus::create([
                "status" => $status,
                "description" => @$data["description"],
                "custom_shipping_id" => $id,
                "packages_count" => @$data["packages_count"],
                "created_by" => auth("web")->id(),
                "date" => $invoice_date ?? $customShipping->date
            ]);
            $customShipping->status = $status;
            if ($status == CustomShippingStatusEnum::PARTIAL_DELIVERED->value) {
                $customShipping->delivered_packages_count +=  $data["packages_count"];
            } else if (
                $status == CustomShippingStatusEnum::FULL_DELIVERED->value ||
                $status == CustomShippingStatusEnum::Distributed->value ||
                $status ==  CustomShippingStatusEnum::Expo_Exported->value
            ) {
                $customShipping->delivered_packages_count =  $customShipping->remained_packages_count;
            }

            $customShipping->save();
            if (isset($data["attachment"])) {
                $original_fielname = $data["attachment"]->getClientOriginalName();
                $mime_type = $data["attachment"]->getClientOriginalExtension();
                $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                $path         = "uploads/custom_shippings/$customShipping->id/status";
                $uploadPath   = public_path($path);
                $data["attachment"]->move($uploadPath, $uniqueName);
                $file_path = $path . "/" . $uniqueName;

                $shippingStatus->attachment()->create([
                    "file_path" => $file_path,
                    "filename" => $uniqueName,
                    "original_filename" => $original_fielname,
                    "mime_type" => $mime_type,
                    "visible_to" => AttachmentVisibleStatus::Employee
                ]);
            }

            // Automatic Status
            if ($customShipping->shipping_type == CustomShippingType::Imported) {
                $this->createImportedShipmentAutomaticStatus($status, $customShipping);
            } else if ($customShipping->shipping_type == CustomShippingType::Exported) {
                $this->createExportedShipmentAutomaticStatus($status, $customShipping);
            }
            $this->createNotificationAfterShippingStatusChanges($customShipping);
            DB::commit();
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function createImportedShipmentAutomaticStatus($status, $customShipping)
    {
        if ($status == CustomShippingStatusEnum::PARTIAL_DELIVERED->value || $status == CustomShippingStatusEnum::FULL_DELIVERED->value) {
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::CustomsClearanceInitiated,
                "description" => null,
                "custom_shipping_id" => $customShipping->id,
                "packages_count" => $customShipping->delivered_packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        } else if ($status == CustomShippingStatusEnum::CustomsClearanceCompleted->value) {
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::UnderCustomsExamination,
                "description" => null,
                "custom_shipping_id" => $customShipping->id,
                "packages_count" => $customShipping->delivered_packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        } else if ($status == CustomShippingStatusEnum::INSPECTED->value) {
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::ShipmentInTransitFromCustoms,
                "description" => null,
                "custom_shipping_id" => $customShipping->id,
                "packages_count" => $customShipping->delivered_packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        }

        if ($status == CustomShippingStatusEnum::RELEASED->value) {
            ShippingInvoice::create([
                "invoice_number" => generateInvoiceNumber(),
                "custom_shipping_id" => $customShipping->id,
                "user_id" =>  $customShipping->assigned_to,
                "status" => ShippingInvoiceStatus::Pending,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        }
    }

    private function createExportedShipmentAutomaticStatus($status, $customShipping)
    {
        if ($status == CustomShippingStatusEnum::Expo_Received->value) {
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::Expo_ClearanceInitiated,
                "description" => null,
                "custom_shipping_id" => $customShipping->id,
                "packages_count" => $customShipping->delivered_packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        } else if ($status == CustomShippingStatusEnum::Expo_ClearanceCompleted->value) {
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::Expo_ThroughExporting,
                "description" => null,
                "custom_shipping_id" => $customShipping->id,
                "packages_count" => $customShipping->delivered_packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        }

        if ($status == CustomShippingStatusEnum::Expo_Exported->value) {
            ShippingInvoice::create([
                "invoice_number" => generateInvoiceNumber(),
                "custom_shipping_id" => $customShipping->id,
                "user_id" =>  $customShipping->assigned_to,
                "status" => ShippingInvoiceStatus::Pending,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->invoice_date ?? $customShipping->date
            ]);
        }
    }
    private function createNewCustomShipping($id, $packages_count)
    {
        try {
            $customShipping = CustomShipping::findOrFail($id);

            $newShipping = CustomShipping::create([
                "policy_number" => $customShipping->policy_number,
                "code" => $customShipping->code,
                "type" => $customShipping->type,
                "client_name" => $customShipping->client_name,
                "client_id" => $customShipping->client_id,
                "shipping_type" => $customShipping->shipping_type,
                "company_id" => $customShipping->company_id,
                "delivery_method" => $customShipping->delivery_method,
                "from_country_id" => $customShipping->from_country_id,
                "country_id" => $customShipping->country_id,
                "air_port_id" => $customShipping->air_port_id,
                "harbor_id" => $customShipping->harbor_id,
                "packages_count" => $packages_count,
                "remained_packages_count" => $packages_count,
                "units_count" => $customShipping->units_count,
                "total_weight" => $customShipping->total_weight,
                "total_size" => $customShipping->total_size,
                "assigned_to" => $customShipping->assigned_to,
                "derived_from" => $customShipping->id,
                "created_by" => auth("web")->id(),
                "status" => CustomShippingStatusEnum::ThroughShipping,
                "date" => $customShipping->date,
            ]);
            $shippingStatus = CustomShippingStatus::create([
                "status" => CustomShippingStatusEnum::ThroughShipping,
                "description" => null,
                "custom_shipping_id" => $newShipping->id,
                "packages_count" => $packages_count,
                "created_by" => auth("web")->id(),
                "date" => $customShipping->date,
            ]);
            return $newShipping;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            if (isset($data["shipping_type"]) && $data["shipping_type"] == CustomShippingType::Imported->value) {
                $data["status"] = CustomShippingStatusEnum::BillOfLadingProcessing;
            }

            if (isset($data["shipping_type"]) && $data["shipping_type"] == CustomShippingType::Exported->value) {
                $data["status"] = CustomShippingStatusEnum::Expo_ThroughShipping;
            }

            $data["remained_packages_count"] = $data["packages_count"];
            $data["created_by"] = auth("web")->id();

            if (isset($data["type"]) && $data["type"] == CustomShippingType::Internal->value) {
                $data["client_id"] = null;
                $data["company_id"] = null;
            }
            if (isset($data["delivery_method"]) && $data["delivery_method"] == DeliveryMethod::ByAir->value) {
                $data["harbor_id"] = null;
            }
            if ($data["delivery_method"] && $data["delivery_method"] == DeliveryMethod::BySea->value) {
                $data["air_port_id"] = null;
            }
            if ($data["delivery_method"] && $data["delivery_method"] == DeliveryMethod::ByLand->value) {
                $data["harbor_id"] = null;
                $data["air_port_id"] = null;
            }
            $shipping = CustomShipping::create($data);

            if (@$data["files_list"]) {
                foreach ($data["files_list"] as $file_request) {
                    $original_fielname = $file_request["attachment"]->getClientOriginalName();
                    $mime_type = $file_request["attachment"]->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/custom_shippings/$shipping->id";
                    $uploadPath   = public_path($path);
                    $file_request["attachment"]->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $shipping->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => $file_request["visible_to"]
                    ]);
                }
            }
            if (isset($data["shipping_type"]) && $data["shipping_type"] == CustomShippingType::Imported->value && isset($data["assigned_to"])) {
                //create shipping status
                CustomShippingStatus::create([
                    "status" => CustomShippingStatusEnum::ASSIGNED,
                    "custom_shipping_id" => $shipping->id,
                    "packages_count" => $shipping->packages_count,
                    "created_by" => auth("web")->id(),
                    'date' => $data["date"]
                ]);
                CustomShippingStatus::create([
                    "status" => CustomShippingStatusEnum::BillOfLadingProcessing,
                    "custom_shipping_id" => $shipping->id,
                    "packages_count" => $shipping->packages_count,
                    "created_by" => auth("web")->id(),
                    'date' => $data["date"]
                ]);
            }
            if (isset($data["shipping_type"]) && $data["shipping_type"] == CustomShippingType::Exported->value) {
                CustomShippingStatus::create([
                    "status" => CustomShippingStatusEnum::Expo_ThroughShipping,
                    "custom_shipping_id" => $shipping->id,
                    "packages_count" => $shipping->packages_count,
                    "created_by" => auth("web")->id(),
                    'date' => $data["date"]
                ]);
            }

            $this->createNotificationAfterShippingCreated($shipping);
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $data["updated_by"] = auth("web")->id();
            $shipping = CustomShipping::findOrFail($id);
            $assigned_to = $shipping->assigned_to;
            if (isset($data["type"]) && $data["type"] == CustomShippingType::Internal->value) {
                $data["client_id"] = null;
                $data["company_id"] = null;
            }
            if (isset($data["delivery_method"]) && $data["delivery_method"] == DeliveryMethod::ByAir->value) {
                $data["harbor_id"] = null;
            }
            if (isset($data["delivery_method"]) && $data["delivery_method"] == DeliveryMethod::BySea->value) {
                $data["air_port_id"] = null;
            }
            if (isset($data["delivery_method"]) && $data["delivery_method"] == DeliveryMethod::ByLand->value) {
                $data["harbor_id"] = null;
                $data["air_port_id"] = null;
            }
            $shipping->update($data);

            // when created their were no broker assigned and now their is one
            if (!$assigned_to && isset($data["assigned_to"])) {
                CustomShippingStatus::create([
                    "status" => CustomShippingStatusEnum::ASSIGNED,
                    "custom_shipping_id" => $shipping->id,
                    "packages_count" => $shipping->packages_count,
                    "created_by" => auth("web")->id(),
                    'date' => $data["date"]
                ]);
                CustomShippingStatus::create([
                    "status" => CustomShippingStatusEnum::BillOfLadingProcessing,
                    "custom_shipping_id" => $shipping->id,
                    "packages_count" => $shipping->packages_count,
                    "created_by" => auth("web")->id(),
                    'date' => $data["date"]
                ]);
            }
            if (@$data["files_list"]) {
                foreach ($data["files_list"] as $file_request) {
                    if (isset($file_request["attachment"])) {
                        $original_fielname = $file_request["attachment"]->getClientOriginalName();
                        $mime_type = $file_request["attachment"]->getClientOriginalExtension();
                        $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                        $path         = "uploads/custom_shippings/$shipping->id";
                        $uploadPath   = public_path($path);
                        $file_request["attachment"]->move($uploadPath, $uniqueName);
                        $file_path = $path . "/" . $uniqueName;

                        $shipping->attachments()->create([
                            "file_path" => $file_path,
                            "filename" => $uniqueName,
                            "original_filename" => $original_fielname,
                            "mime_type" => $mime_type,
                            "visible_to" => $file_request["visible_to"]
                        ]);
                    } elseif (isset($file_request["attachment_id"])) {
                        Attachment::where('id', $file_request["attachment_id"])->update([
                            "visible_to" => $file_request["visible_to"]
                        ]);
                    }
                }
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $customShipping = CustomShipping::with("shippingInvoices", "attachments")->findOrFail($id);
            $customShipping->shippingInvoices->each(function ($invoice) {
                $this->shippingInvoiceService->deleteInvoice($invoice->id);
            });
            $customShipping->attachments->each(function ($attachment) {
                $item = Attachment::findOrFail($attachment->id);
                $item->delete();
            });
            $customShipping->delete();
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
    public function createNotificationAfterShippingCreated($customShipping)
    {
        $customShipping = $customShipping->load("client.user");
        $to = [];
        if (isset($customShipping->assigned_to)) {
            array_push($to, $customShipping->assigned_to);
        }
        if ($customShipping->type == CustomShippingType::External) {
            array_push($to,  @$customShipping->client->user->id);
        }
        if (count($to) > 0) {
            $params = [
                "title" => __("New Custom Shipping Added"),
                "content" => __("New Custom Shipping Is added To You Of Plicy Number") . " " . $customShipping->policy_number,
                "to" => $to,
                "from_user_id" => auth("web")->id(),
                "route" => "admin.custom_shippings.show",
                "routeParams" => $customShipping->id,
                "reference_id" => $customShipping->id,
                "reference_type" => get_class($customShipping)
            ];
            (new NotificationsService())->createNotification($params);
        }
    }
    public function createNotificationAfterShippingPartiallyDelivered($customShipping)
    {
        $customShipping = $customShipping->load("client.user");
        $to = [];
        if ($customShipping->type == CustomShippingType::External) {
            array_push($to,  @$customShipping->client->user->id);
        }
        if (count($to) > 0) {
            $params = [
                "title" => __("New Custom Shipping Added"),
                "content" => __("New Custom Shipping Is added To You Of Plicy Number") . " " . $customShipping->policy_number,
                "to" => $to,
                "from_user_id" => auth("web")->id(),
                "route" => "admin.custom_shippings.show",
                "routeParams" => $customShipping->id,
                "reference_id" => $customShipping->id,
                "reference_type" => get_class($customShipping)
            ];
            (new NotificationsService())->createNotification($params);
        }
    }

    public function createNotificationAfterShippingStatusChanges($customShipping)
    {
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $customShipping = $customShipping->load("client.user");
        if ($customShipping->type == CustomShippingType::External) {
            array_push($to,  @$customShipping->client->user->id);
        }
        if (count($to) > 0) {
            $params = [
                "title" => __("The Custom Shipping changed To") . " " . __($customShipping->status->label()),
                "content" => __("The Custom Shipping Status Is Changed To") . " " . __($customShipping->status->label()) .  __("Of Plicy Number") . $customShipping->policy_number,
                "to" => $to,
                "from_user_id" => $customShipping->assigned_to,
                "route" => "admin.custom_shippings.show",
                "routeParams" => $customShipping->id,
                "reference_id" => $customShipping->id,
                "reference_type" => get_class($customShipping)
            ];
            (new NotificationsService())->createNotification($params);
        }
    }
}
