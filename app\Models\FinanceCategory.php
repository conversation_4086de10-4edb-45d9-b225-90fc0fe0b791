<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinanceCategory extends Model
{
    protected $fillable = [
        "name",
        "name_ar",
        "name_en",
        "status",
        "created_by",
        "updated_by"
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }
    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }
}
