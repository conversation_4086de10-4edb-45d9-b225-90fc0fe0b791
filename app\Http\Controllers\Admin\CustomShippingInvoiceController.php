<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\CustomShipping;
use App\Models\ShippingExpense;
use App\Models\ShippingInvoice;
use App\Models\ShippingExpenseType;
use App\Http\Controllers\Controller;
use App\Constants\ShippingInvoiceStatus;
use App\Constants\UserType;
use App\Models\Setting;
use App\Services\ShippingInvoiceService;
use Illuminate\Support\Facades\Log;

class CustomShippingInvoiceController extends Controller
{
    protected $shippingInvoiceService;

    public function __construct()
    {
        $this->shippingInvoiceService = new ShippingInvoiceService();
        $this->middleware('is_able:Create Shipping Invoice')->only(['create', 'store']);
        $this->middleware('is_able_any:View Shipping Invoices,View Related Shipping Invoices')->only(['index', 'view']);
        $this->middleware('is_able:Update Shipping Invoice')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Shipping Invoice')->only(['destroy']);
    }
    public function index()
    {
        try {
            $type = request()->query('type');
            if ($type == UserType::Client->value && is_admin()) {
                $data["title"] = __("Clients Invoices");
                $data["sub_title"] = __("Clients Invoices");
            } else if ($type == UserType::Broker->value && is_admin()) {
                $data["title"] = __("Workers Invoices");
                $data["sub_title"] = __("Workers Invoices");
            } else {
                $data["title"] = __("Shipping Invoices");
                $data["sub_title"] = __("Shipping Invoices");
            }
            $data["type"] = $type;
            return view('admin.shipping_invoices.invoices', $data);
        } catch (Exception $ex) {
            session()->flash("error", $ex->getMessage());
            return redirect()->back();
        }
    }
    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";
            $userType = $request->UserType;
            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $user = User::with("client")->find(auth("web")->id());
            $query = ShippingInvoice::with("customShipping", "shippingExpenses", "attachments.users", "users")
                ->when($user, function ($query) use ($user) {
                    if ($user->type == UserType::Broker && !$user->IsSuperAdmin) {
                        $query->where("user_id", $user->id);
                    } else if ($user->type == UserType::Client) {
                        $query->where("user_id", $user->id)->where("status", ShippingInvoiceStatus::Approved->value);
                    }
                })->when($userType, function ($query) use ($userType) {
                    if ($userType == UserType::Client->value) {
                        $query->whereHas("user", function ($q) {
                            $q->where("type", UserType::Client->value);
                        });
                    } else if ($userType == UserType::Broker->value) {
                        $query->whereHas("user", function ($q) {
                            $q->where("type", UserType::Broker->value);
                        });
                    }
                })
                ->when($search, function ($query) use ($search) {
                    $query
                        ->whereHas("customShipping", function ($q) use ($search) {
                            $q->where("policy_number", "LIKE", "%" . $search . "%");
                        });
                });
            $TotalCount = $query->count();

            if ((in_array($orderBy, ["policy_number", "packages_count", "total_weight", "total_size", "code"]))) {
                $query = $query
                    ->leftJoin('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->orderBy("csh.{$orderBy}", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "company_name") {
                $query = $query
                    ->leftJoin('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->leftJoin("companies as c", "c.id", "=", "csh.company_id")
                    ->orderBy(app()->getLocale() == "ar" ? "c.name" : "c.name", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "from_country") {
                $query = $query
                    ->leftJoin('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->leftJoin("countries as c", "c.id", "=", "csh.from_country_id")
                    ->orderBy(app()->getLocale() == "ar" ? "c.name_ar" : "c.name_en", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "total_amount") {
                $query = $query
                    ->leftJoin('shipping_expenses as exp', 'exp.shipping_invoice_id', '=', 'shipping_invoices.id')
                    ->selectRaw('shipping_invoices.*, SUM(exp.amount) as total_amount')
                    ->groupBy('shipping_invoices.id')
                    ->orderBy('total_amount', $direction);
            } else if ($orderBy == "user_name") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'shipping_invoices.user_id')
                    ->orderBy("u.name", $direction)->select("shipping_invoices.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;
            $data["userType"] = $userType;
            $view = view("admin.shipping_invoices.invoices_list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    public function create()
    {
        $data["title"] = __("Shipping Invoice");
        $data["sub_title"] = __("Add Shipping Invoice");

        $data["shippingExpenseTypes"] = ShippingExpenseType::all();
        $data["shippingInvoiceStatus"] = ShippingInvoiceStatus::availableTypes();
        $data["customShippings"] = CustomShipping::all(['policy_number', 'id', "packages_count"]);
        $data["clients"] = User::where("type", UserType::Client->value)->get(['id', "name_ar", "name_en", "name"]);

        $data["invoice_number"] = generateClientInvoiceNumber();
        return view('admin.shipping_invoices.create', $data);
    }
    public function store(Request $request)
    {
        $request->validate([
            'shipping_expense.*.shipping_expense_type_id' => 'required|integer',
            'shipping_expense.*.amount' => 'required|numeric',
            'shipping_expense.*.notes' => 'nullable|string',
            "invoice_number" => "required|integer",
            "user_id" => "required|integer",
            "custom_shipping_id" => "nullable",
            "attachments" => "nullable|array",
            "notes" => "nullable",
            "users" => "nullable|array"
        ]);
        try {
            $data = $request->all();
            $data["status"] = ShippingInvoiceStatus::Approved;
            $data["created_by"] = auth("web")->id();
            $this->shippingInvoiceService->storeClientInvoice($data);
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return back();
        } catch (Exception $ex) {
            session()->flash("error", $ex->getMessage());
            return back();
        }
    }
    public function edit($id)
    {
        $data["title"] = __("Shipping Invoice");
        $data["sub_title"] = __("Shipping Expenses");

        $data["shippingInvoice"] = ShippingInvoice::with("shippingExpenses", "attachments.users", "user.client", "users")->where("id", $id)->first();

        $data["shippingExpenseTypes"] = ShippingExpenseType::all();

        $data["shippingInvoiceStatus"] = ShippingInvoiceStatus::availableTypes();
        $data["custom_shipping_id"] =  $data["shippingInvoice"]->custom_shipping_id;

        if ($data["shippingInvoice"]->user?->type == UserType::Client) {
            $data["sub_clients"] = User::where('client_id', $data["shippingInvoice"]->user?->client->id)->get();
            $data["users"] = User::where('client_id', $data["shippingInvoice"]->user?->client->id)
                ->orWhere("id", $data["shippingInvoice"]->user?->client->user_id)
                ->get();
        }
        return view('admin.shipping_invoices.edit', $data);
    }
    public function update(Request $request, $id)
    {
        // return $request->all();
        $request->validate([
            'shipping_expense.*.shipping_expense_type_id' => 'required|integer',
            'shipping_expense.*.amount' => 'required|numeric',
            'shipping_expense.*.notes' => 'nullable|string',
            "invoice_id" => "required|integer",
            "attachments" => "nullable|array",
            "notes" => "nullable",
            "users" => "nullable|array"
        ]);
        try {
            $this->shippingInvoiceService->createOrUpdateInvoice($request->all());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return back();
        } catch (Exception $ex) {
            session()->flash("error", $ex->getMessage());
            return back();
        }
    }

    public function getUserInvoices($user_id)
    {
        try {
            $user = User::findOrFail($user_id);
            $data["title"] = __("Shipping Invoices") . " " . __("Of") . " " . $user->name;
            $data["sub_title"] = __("Shipping Invoices") . " " . $user->name;
            $data["user_id"] = $user_id;

            return view('admin.shipping_invoices.user_invoices', $data);
        } catch (Exception $ex) {
            session()->flash("error", $ex->getMessage());
            return redirect()->back();
        }
    }

    public function getUserInvoicesList(Request $request, $user_id)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = ShippingInvoice::with("customShipping", "shippingExpenses")
                ->where("user_id", $user_id)
                ->when($search, function ($query) use ($search) {
                    $query
                        ->whereHas("customShipping", function ($q) use ($search) {
                            $q->where("policy_number", "LIKE", "%" . $search . "%");
                        });
                });
            $TotalCount = $query->count();

            if ((in_array($orderBy, ["policy_number", "packages_count", "total_weight", "total_size", "code"]))) {
                $query = $query
                    ->join('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->orderBy("csh.{$orderBy}", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "company_name") {
                $query = $query
                    ->leftJoin('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->leftJoin("companies as c", "c.id", "=", "csh.company_id")
                    ->orderBy(app()->getLocale() == "ar" ? "c.name" : "c.name", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "from_country") {
                $query = $query
                    ->leftJoin('custom_shippings as csh', 'csh.id', '=', 'shipping_invoices.custom_shipping_id')
                    ->leftJoin("countries as c", "c.id", "=", "csh.from_country_id")
                    ->orderBy(app()->getLocale() == "ar" ? "c.name_ar" : "c.name_en", $direction)->select("shipping_invoices.*");
            } else if ($orderBy == "total_amount") {
                $query = $query
                    ->leftJoin('shipping_expenses as exp', 'exp.shipping_invoice_id', '=', 'shipping_invoices.id')
                    ->selectRaw('shipping_invoices.*, SUM(exp.amount) as total_amount')
                    ->groupBy('shipping_invoices.id')
                    ->orderBy('total_amount', $direction);
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.shipping_invoices.user_invoices_list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    public function createInvoiceClient($id)
    {
        try {
            $invoice = ShippingInvoice::findOrFail($id);
            $invoice = $invoice->load("customShipping");
            if (@$invoice->customShipping && $invoice->customShipping->client_id == null) {
                throw new Exception(__("No Client Found For This Custom Shipping"));
            }
            $id = $this->shippingInvoiceService->createClientInvoice($id);
            return response()->json(["success" => true, "id" => $id]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, 'error' => $ex->getMessage()]);
        }
    }
    public function deleteExpense($id)
    {
        try {
            ShippingExpense::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function destroy($id)
    {
        try {
            $this->shippingInvoiceService->deleteInvoice($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportInvoicePdf($id)
    {
        $logo = Setting::where("key", "logo")->first()->value;

        $lang = request()->query("lang") ?? "ar";
        if ($lang != "ar" && $lang != "en") {
            return back();
        }
        app()->setLocale($lang);
        $invoice =  ShippingInvoice::with("customShipping", "shippingExpenses.shippingExpenseType", "user")
            ->where("id", $id)->first();

        $user = User::with("client")->find(auth("web")->id());
        if (!$user->IsSuperAdmin &&  $invoice->user_id != $user->id) {
            return back();
        }
        return view("admin.shipping_invoices.invoice_print", compact("invoice", "logo"));

        // $mpdf = new \Mpdf\Mpdf([
        //     'mode'        => 'utf-8',
        //     'format'      => 'A4',
        //     'orientation' => 'landscape',
        // ]);
        // $mpdf->shrink_tables_to_fit = 0;
        // $mpdf->autoScriptToLang         = true;
        // $mpdf->autoLangToFont           = false;
        // $mpdf->allow_charset_conversion = false;
        // $mpdf->setAutoTopMargin = 'pad';
        // $mpdf->setAutoBottomMargin  = 'pad';
        // $mpdf->falseBoldWeight = 10;
        // $mpdf->WriteHTML(view('admin.shipping_invoices.invoice_pdf', compact('invoice', 'logo', "mpdf")));
        // $mpdf->Output(time() . "_shipping_invoice" . '.pdf', 'D');
    }
}
