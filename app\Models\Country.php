<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    protected $fillable = [
        "name",
        "name_ar",
        "name_en",
        "short_name",
        "iso3",
        "number_code",
        "phone_code",
        "is_default",
        "type"
    ];

    protected $casts = [
        "is_default" => "boolean"
    ];
    public function airports(): HasMany
    {
        return $this->hasMany(AirPort::class);
    }
    public function harbors(): HasMany
    {
        return $this->hasMany(Harbor::class);
    }

    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }
}
