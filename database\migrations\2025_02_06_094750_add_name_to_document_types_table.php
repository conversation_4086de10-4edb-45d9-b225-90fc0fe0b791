<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_types', function (Blueprint $table) {
            $table->string('name_ar', 200)->nullable();
            $table->string('name_en', 200)->nullable();
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->string('name_ar', 200)->nullable();
            $table->string('name_en', 200)->nullable();
        });
        Schema::table('expense_types', function (Blueprint $table) {
            $table->string('name_ar', 200)->nullable();
            $table->string('name_en', 200)->nullable();
        });
        Schema::table('finance_categories', function (Blueprint $table) {
            $table->string('name_ar', 200)->nullable();
            $table->string('name_en', 200)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_types', function (Blueprint $table) {
            //
        });
    }
};
