<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DocumentType extends Model
{
    protected $fillable = [
        "name_ar",
        "name_en",
        "name",
        "status",
        "created_by",
        "updated_by"
    ];

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }
    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }
}
