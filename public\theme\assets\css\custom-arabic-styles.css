/**
 * Custom Arabic Styles for UI Components
 * This file contains styles for Arabic customized components
 */

/* File Input Wrapper Styles */
.file-input-wrapper {
    display: flex;
    align-items: center;
    border: 1px solid #d9dee3;
    border-radius: 0.375rem;
    padding: 0.4375rem 0.875rem;
    background-color: #fff;
    min-height: 38px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.file-input-wrapper:hover {
    border-color: #696cff;
}

.file-input-wrapper:focus-within {
    border-color: #696cff;
    box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

/* File Input Button Styles */
.file-input-button {
    border: none !important;
    background: transparent !important;
    color: #696cff !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    white-space: nowrap;
    order: 1; 
    flex-shrink: 0; 
}

.file-input-button:hover {
    color: #5f61e6 !important;
    background: transparent !important;
    border: none !important;
}

.file-input-button:focus {
    color: #5f61e6 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* File Name Display Styles */
.file-name {
    flex: 1;
    font-size: 0.9375rem;
    margin-right: 0.5rem; 
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right; 
    order: 2; 
    min-width: 0; 
}

/* LTR Support for English */
[dir="ltr"] .file-name {
    margin-right: 0;
    margin-left: 0.5rem;
    text-align: left;
    order: 1; 
}

[dir="ltr"] .file-input-button {
    order: 2; 
}

/* Additional RTL Support */
[dir="rtl"] .file-input-wrapper {
    flex-direction: row; 
}

[dir="ltr"] .file-input-wrapper {
    flex-direction: row;
}

/* Multiple Files Indicator */
.file-name.multiple-files {
    color: #28a745 !important;
    font-weight: 500;
}

/* Error State */
.file-input-wrapper.is-invalid {
    border-color: #dc3545;
}

.file-input-wrapper.is-invalid:focus-within {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Success State */
.file-input-wrapper.is-valid {
    border-color: #28a745;
}

.file-input-wrapper.is-valid:focus-within {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Dark Mode Support */
[data-bs-theme="dark"] .file-input-wrapper {
    background-color: #2b2c40;
    border-color: #444564;
    color: #a3a4cc;
}

[data-bs-theme="dark"] .file-input-wrapper:hover {
    border-color: #696cff;
}

[data-bs-theme="dark"] .file-name {
    color: #a3a4cc;
}

[data-bs-theme="dark"] .file-name.text-dark {
    color: #fff !important;
}

/* Responsive Design */
@media (max-width: 576px) {
    .file-input-wrapper {
        flex-direction: column;
        align-items: flex-start;
        padding: 0.5rem;
    }

    .file-name {
        margin-left: 0;
        margin-right: 0;
        margin-top: 0.25rem;
        width: 100%;
        text-align: center;
    }

    .file-input-button {
        width: 100%;
        text-align: center;
    }
}
