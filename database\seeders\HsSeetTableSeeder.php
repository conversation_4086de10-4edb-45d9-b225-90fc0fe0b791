<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Models\HsSheet;
use Illuminate\Database\Seeder;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class HsSeetTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sheet_path = public_path("uploads/hs_sheet_2025.xlsx");
        $data_list = [];
        if (!file_exists($sheet_path)) {
            $this->command->error("Excel file not found at: $sheet_path");
            return;
        }

        // Read Excel data in chunks
        // Load the Excel file
        $rows = Excel::toCollection(null, $sheet_path)->first();

        // Process in chunks
        $rows->chunk(1000)->each(function ($chunk) {
            $dataList = [];

            foreach ($chunk as $key => $row) {
                // Skip header row
                if ($key === 0) continue;
                //  الهيئة العامة للغذاء والتغذية  (2)   (على مستوى الفصل)  -------------------- 
                //جهة اخرى  (99)   (على مستوى بند التعرفة)  -------------------- 

                $restriction_import = array_filter(explode(" -------------------- ", @$row[15]));
                $prohibition_import = array_filter(explode(" -------------------- ", @$row[16]));
                $restriction_export = array_filter(explode(" -------------------- ", @$row[17]));
                $prohibition_export = array_filter(explode(" -------------------- ", @$row[18]));

                $dataList[] = [
                    'section_code' => @$row[0],
                    'section_name_en' => @$row[1],
                    'section_name_ar' => @$row[2],
                    'chapter_code' => @$row[3],
                    'chapter_name_en' => @$row[4],
                    'chapter_name_ar' => @$row[5],
                    'heading_code' => @$row[6],
                    'heading_name_en' => @$row[7],
                    'heading_name_ar' => @$row[8],
                    'hs_code' => @$row[9],
                    'duty' => @$row[10],
                    'name_en' => @$row[11],
                    'name_ar' => @$row[12],
                    'description' => @$row[13],
                    'unit' => @$row[14],
                    'restriction_import' => json_encode($restriction_import),
                    'prohibition_import' => json_encode($prohibition_import),
                    'restriction_export' => json_encode($restriction_export),
                    'prohibition_export' => json_encode($prohibition_export),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert data in bulk
            if (!empty($dataList)) {
                HsSheet::insert($dataList);
            }
        });

        $this->command->info('Excel data imported successfully!');
    }
}
