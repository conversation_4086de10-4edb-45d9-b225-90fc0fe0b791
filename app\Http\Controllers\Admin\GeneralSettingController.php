<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\Setting;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;

class GeneralSettingController extends Controller
{
    public function index()
    {
        $data = [];

        $data["title"] = __("General Settings");
        $settings = Setting::where('type', "general_setting")->get();
        $items = $settings->flatMap(function ($item) {
            return [$item->key  => $item->value];
        });
        $data["items"] = $items;
        return view("admin.settings.index", $data);
    }

    public function update(Request $request)
    {
        try {
            $settings = $request->all();
            if (isset($settings['logo'])) {
                $settings['logo'] =  $this->uploadImage($request, 'logo');
            }

            if (isset($settings['favicon'])) {
                $settings['favicon'] =  $this->uploadImage($request, 'favicon');
            }
            foreach ($settings as $key => $value) {
                if ($key != '_token') {
                    $setting = Setting::where('key', $key)->first();
                    if (isset($setting)) {
                        $setting->update(['value' => $value]);
                    } else {
                        Setting::create([
                            "key" => $key,
                            "value" => $value,
                            "type" => "general_setting"
                        ]);
                    }
                }
            }
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Upload image
     * @param Request $request
     * @param mixed $old
     * 
     * @return string|null
     */
    public function uploadImage(Request $request, $filename, $old = null)
    {
        $avatar = null;
        if ($request->hasFile($filename)) {
            $image = $request->file($filename);
            if (isset($image)) {
                $fileName     = $request->file($filename);
                $uniqueName   = strtolower(Str::uuid() . '.' . $fileName->getClientOriginalExtension());
                $path         = 'uploads/settings';
                $uploadPath   = public_path($path);
                $fileName->move($uploadPath, $uniqueName);
                $avatar = $path . "/" . $uniqueName;

                if (isset($old) && File::exists(public_path($old))) {
                    File::delete(public_path($old));
                }
            }
        }
        return $avatar;
    }
}
