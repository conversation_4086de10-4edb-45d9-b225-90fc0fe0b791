<?php

namespace App\Console\Commands;

use App\Constants\FinancialMonthStatus;
use Carbon\Carbon;
use App\Models\FinancialYear;
use App\Models\FinancialMonth;
use Illuminate\Console\Command;

class GenerateFinancialYear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-financial-year';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the current year
        $currentYear = Carbon::now()->year;

        // Generate the start and end dates for the year
        $startFrom = Carbon::createFromDate($currentYear, 1, 1)->startOfDay();
        $endTo = Carbon::createFromDate($currentYear, 12, 31)->endOfDay();

        // Check if a financial year already exists for this range
        $existingYear = FinancialYear::where('start_from', $startFrom)
            ->where('end_to', $endTo)
            ->first();

        if ($existingYear) {
            return;
        }

        // Create the financial year
        $financialYear = FinancialYear::create([
            'name' =>  $currentYear,
            'description' => 'Financial Year ' . $currentYear,
            'start_from' => $startFrom,
            'end_to' => $endTo,
            'status' => 1, // Mark as the current year
        ]);

        // Generate financial months
        $startDate = $startFrom->copy();

        $is_first = true;
        while ($startDate->lessThanOrEqualTo($endTo)) {
            $monthStart = $startDate->copy()->startOfMonth();
            $monthEnd = $startDate->copy()->endOfMonth();

            FinancialMonth::create([
                'financial_year_id' => $financialYear->id,
                'name' => $startDate->format('F'), // Month name
                'start_from' => $monthStart,
                'end_to' => $monthEnd,
                "start_balance" => 0.00,
                "end_balance" => 0.00,
                "total_revenues" => 0.00,
                "total_expenses" => 0.00,
                "withdrawn_revenues" => 0.00,
                "status" => $is_first ? FinancialMonthStatus::Opened : FinancialMonthStatus::Closed
            ]);
            $is_first = false;
            // Move to the next month
            $startDate->addMonthNoOverflow();
        }

        return;
    }
}
