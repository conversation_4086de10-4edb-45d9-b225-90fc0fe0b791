<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ShippingExpenseType;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class ShippingExpenseTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $list = [
            [
                "name_ar" => "رســـــــوم جمــــــركية",
                "name_en" => "CUSTOMS DUTY",
                "status" => 1
            ],
            [
                "name_ar" => "رســــوم جلـــــوبــال",
                "name_en" => "GLOBAL",
                "status" => 1
            ],
            [
                "name_ar" => " تصديقات خارجية",
                "name_en" => "LEGALIZATION",
                "status" => 1
            ],
            [
                "name_ar" => "أرضيات(ناشيونال-الكويتية)",
                "name_en" => "NAS / KAC CHARGES",
                "status" => 1
            ],
            [
                "name_ar" => "إستـــــــــــلام بــولــيصة الشحن",
                "name_en" => "DELIVERY ORDER",
                "status" => 1
            ],
            [
                "name_ar" => "أجــــــــور تخليــــــــص",
                "name_en" => "CUSTOMS CLEARANCE",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج المعادن الثمينة",
                "name_en" => " MOCI APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج وزراة المواصلات",
                "name_en" => "MOC APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج الهيئة العامة للصناعة",
                "name_en" => "PAI APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج وزراة الداخلية",
                "name_en" => " MOI APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج الهيئة العامة للبيئة",
                "name_en" => "EPA APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج وزراة الصحة",
                "name_en" => "MOH APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "إفراج الهيئة العامة للزارعة",
                "name_en" => "AGRICULTURE APPROVAL",
                "status" => 1
            ],
            [
                "name_ar" => "أجـــــور تفتيش الشحنة",
                "name_en" => "INSPECTION",
                "status" => 1
            ],
            [
                "name_ar" => "أجــــــــــور نقل الشحنة",
                "name_en" => "TRANSPORT",
                "status" => 1
            ],
            [
                "name_ar" => "أجــــــــــور عمــال المطار",
                "name_en" => "LABOUR CHARGES Airport",
                "status" => 1
            ],
            [
                "name_ar" => "أجور عمال أخري",
                "name_en" => "LABOUR CHARGES OTHERS",
                "status" => 1
            ],
            [
                "name_ar" => "أجــــــــــور رافعات",
                "name_en" => "F/LIFT",
                "status" => 1
            ],
            [
                "name_ar" => "طبـــــاعـة البيــــان",
                "name_en" => " BAYAN PRINT",
                "status" => 1
            ],
            [
                "name_ar" => "خدمــــــــــات أخري",
                "name_en" => "OTHERS",
                "status" => 1
            ],
        ];

        // ShippingExpenseType::insert($list);
        $sheet_path = public_path("uploads/shipping_expense_types.xlsx");
        $rows = Excel::toCollection(null, $sheet_path)->first();
        $data = [];
        foreach ($rows as $index => $type) {
            if ($index == 0) continue;

            $data[] = [
                "name_ar" => $type[0],
                "name_en" => $type[1],
                "status" => 1
            ];
        }

        foreach ($data as $item) {
            ShippingExpenseType::updateOrCreate(["name_ar" => $item["name_ar"]], [
                "name_en" => $item["name_en"],
                "status" => $item["status"]
            ]);
        }
    }
}
