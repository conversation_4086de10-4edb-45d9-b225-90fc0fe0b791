<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('withdraw_revenues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('financial_month_id')->nullable()->constrained('financial_months')->nullOnDelete();
            $table->date("date")->nullable();
            $table->decimal("amount", 10, 2)->nullable();
            $table->text("description")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdraw_revenues');
    }
};
