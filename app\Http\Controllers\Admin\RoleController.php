<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use App\Constants\UserType;
use Exception;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware('is_able:Create Role')->only(['create', 'store']);
        $this->middleware('is_able:View Roles')->only(['index', 'view']);
        $this->middleware('is_able:Update Role')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Role')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $data["title"] = __("Roles");
        $data["sub_title"] = __("Roles List");
        return view('admin.roles.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Role::with('users')->when($search, function ($query) use ($search) {
                $query->where("name", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.roles.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            $data["title"] = __("Roles");
            $data["sub_title"] = __("Add New Role");
            $data["users"] = User::where(['status' => 'Active'])
                // ->where('type', UserType::Employee->value)
                ->where("IsSuperAdmin", "!=", 1)
                ->get();
            $data["permissions"] = Permission::get(["id", "name", "group"])->groupBy("group");
            return view('admin.roles.create', $data);
        } catch (Exception $e) {
            return $e;
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->only(['name']);
            $role = Role::create($data);

            if ($request->permissions) {
                $role->syncPermissions($request->permissions);
            }
            if ($request->users) {
                //assign to role
                foreach ($request->users as $id) {
                    $user = User::find($id);
                    $user->assignRole([$request->name]);
                }
            }
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            return $e;
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('admin.show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $role = Role::findById($id);
            $data["title"] = __("Roles");
            $data["sub_title"] = __("Edit Role");

            $data["users"] = User::with('roles')
                ->where("IsSuperAdmin", "!=", 1)
                ->get()
                ->filter(
                    fn($user) => !$user->hasRole($role->name)
                )
                ->all();
            $data["permissions"] = Permission::all();

            $data["assigned_users"] = User::with('roles')
                ->where("IsSuperAdmin", "!=", 1)
                ->get()->filter(
                    fn($user) => $user->hasRole($role->name)
                )->all();
            $data["role"] = $role;
            return view('admin.roles.edit', $data);
        } catch (Exception $e) {
            return $e;
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $role = Role::findById($id);
            $role->update([
                'name' => $request->name,
            ]);

            if ($request->permissions) {
                $role->syncPermissions($request->permissions);
            }
            if ($request->users) {
                //assign to role
                foreach ($request->users as $id) {
                    $user = User::find($id);
                    $user->assignRole([$role->name]);
                }
            }
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            return $e;
        }
    }

    public function removeRoleFromUser($roleId, $userId)
    {
        try {
            $role = Role::findById($roleId);
            $user = User::find($userId);
            $user->removeRole($role->name);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            Role::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
}
