<?php

namespace App\Http\Requests\Admin\Client;

use App\Constants\CrudMessage;
use App\Models\Client;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $client = Client::findOrFail($this->client);
        return [
            "name_ar" => "required|max:100",
            "name_en" => "required|max:100",
            "username" => "required_if:can_login,1|unique:users,username," . $client->user_id,
            "email" => "required_if:can_login,1|unique:users,email," . $client->user_id,
            "phone" => "required",
            "address" => "nullable",
            "company_name" => "nullable",
            "can_login" => "nullable",
        ];
    }


    public function messages(): array
    {
        return [
            "name_ar.required" => CrudMessage::isRequired(__("Arabic Name")),
            "name_ar.max" => CrudMessage::maxLengthCharacters(100),
            "name_en.required" => CrudMessage::isRequired(__("English Name")),
            "name_en.max" => CrudMessage::maxLengthCharacters(100),
            "username.required_if" => CrudMessage::isRequired(__("username")),
            "email.required_if" => CrudMessage::isRequired(__("email")),
            "email.unique" => CrudMessage::alreadyExists(__("email")),
            "password.required_if" => CrudMessage::isRequired(__("password")),
            "password.confirmed" => __("Password does not match"),
            "password.min" => CrudMessage::minLengthCharacters(6),
            "password.max" => CrudMessage::maxLengthCharacters(20),
            "password_confirmation.required_if" => CrudMessage::isRequired(__("password confirmation")),
        ];
    }
}
