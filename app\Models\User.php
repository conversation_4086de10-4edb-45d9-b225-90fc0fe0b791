<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Constants\UserType;
use App\Models\Notification;
use App\Models\Traits\Auditable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;


class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guard_name = 'web';
    protected $fillable = [
        "username",
        'name',
        "name_ar",
        "name_en",
        'email',
        'password',
        'status',
        'type',
        'phone',
        'address',
        'image',
        "client_id",
        'IsSuperAdmin'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            "type" => UserType::class
        ];
    }

    public function client(): HasOne
    {
        return $this->hasOne(Client::class);
    }
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Client::class, "client_id");
    }
    public function avatar(): MorphOne
    {
        return $this->morphOne(Attachment::class, "attachmentable");
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    public function workerDue(): HasOne
    {
        return $this->hasOne(WorkerDue::class);
    }
    public function invoices(): HasMany
    {
        return $this->hasMany(ShippingInvoice::class);
    }
    public function receivedAdvances(): HasMany
    {
        return $this->hasMany(Advance::class, "to_user_id");
    }
    public function sentAdvances(): HasMany
    {
        return $this->hasMany(Advance::class, "from_user_id");
    }
    public function wallet(): HasOne
    {
        return $this->hasOne(Wallet::class);
    }

    public function documentTypes(): BelongsToMany
    {
        return $this->belongsToMany(DocumentType::class);
    }

    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class);
    }
    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }

    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.users.edit");
    }
}
