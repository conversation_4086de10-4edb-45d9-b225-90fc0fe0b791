<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\WorkerDue;
use App\Constants\UserType;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Constants\WorkerDueAction;
use App\Services\WorkerDueService;
use App\Http\Controllers\Controller;
use App\Services\PdfService;

class WorkerDueController extends Controller
{
    protected $workerDueService;
    public function __construct()
    {
        $this->workerDueService = new WorkerDueService();
        $this->middleware('is_able:Create Worker Due')->only(['create', 'store']);
        // $this->middleware('is_able:View_Worker_Dues')->only(['index', 'view']);
        $this->middleware('is_able:Update Worker Due')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Worker Due')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (is_admin()) {
            $data["title"] = __("Workers Dues");
            $data["sub_title"] = __("Workers Dues");
        } else {
            $data["title"] = __("Financial Dues");
            $data["sub_title"] = __("Financial Dues");
        }

        return view('admin.worker_dues.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $user = User::find(auth("web")->id());
            $query = User::has("workerDue", ">=", 1)->with("workerDue", "invoices", "receivedAdvances", "wallet")->when($user, function ($query) use ($user) {
                if ($user->type == UserType::Broker && $user->can('View Related Worker Dues')) {
                    $query->where("id", $user->id);
                }
            })->when($search, function ($query) use ($search) {
                $query->where("name", "LIKE", "%" . $search . "%")
                    ->orWhere("email", "LIKE", "%" . $search . "%");
            });
            // $query = WorkerDue::with("user")
            //     ->when($user, function ($query) use ($user) {
            //         if ($user->type == UserType::Employee && $user->can('View_Related_Worker_Dues')) {
            //             $query->where("user_id", $user->id);
            //         }
            //     })
            //     ->when($search, function ($query) use ($search) {
            //         $query->whereHas("user", function ($q) use ($search) {
            //             $q->where("name", "LIKE", "%" . $search . "%")
            //                 ->orWhere("email", "LIKE", "%" . $search . "%");
            //         });
            //     });
            $TotalCount = $query->count();

            if ($orderBy == "current_balance") {
                $query = $query
                    ->leftJoin('worker_dues as w', 'w.user_id', '=', 'users.id')
                    ->orderBy('w.amount', $direction)->select("users.*");
            } else if ($orderBy == "total_dues") {
                $query = $query
                    ->leftJoin('shipping_invoices as i', 'i.user_id', '=', 'users.id')
                    ->leftJoin('shipping_expenses as se', 'se.shipping_invoice_id', '=', 'i.id')
                    ->selectRaw('users.*, COALESCE(SUM(se.amount), 0) as total_dues')
                    ->groupBy('users.id')
                    ->orderBy('total_dues', $direction);
            } else if ($orderBy == "received_advances") {
                $query = $query
                    ->leftJoin('advances as a', 'a.to_user_id', '=', 'users.id')
                    ->selectRaw('users.*, COALESCE(SUM(a.amount), 0) as received_advances')
                    ->groupBy('users.id')
                    ->orderBy('received_advances', $direction);
            } else if ($orderBy == "wallet_balance") {
                $query = $query
                    ->leftJoin('wallets as w', 'w.user_id', '=', 'users.id')
                    ->orderBy("w.amount", $direction)->select("users.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $users = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.worker_dues.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Worker Dues");
        $data["sub_title"] = __("add New");
        $data["users"] = User::where('IsSuperAdmin', '!=', 1)->get();
        return view('admin.worker_dues.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {

            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('admin.show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $data["title"] = __("Users");
            $data["sub_title"] = __("update user");

            $data["user"] = User::findOrFail($id);

            return view('admin.worker_wages.edit', $data);
        } catch (Exception $e) {
            session()->flash("error", $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $this->workerDueService->updateWorkerDue($request->all(), $id);
            session()->flash("success", __("data is updated successfully"));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __("something went wrong, please try again"));
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $user = User::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = "";
        $users = User::where("type", "employee")
            ->where('id', '!=', auth("web")->id())
            ->where("IsSuperAdmin", "!=", 1)->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.users.export_pdf', [
            'users' => $users,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_users.pdf", 'D');
    }

    public function exportAllToExcel()
    {
        // return Excel::download(new UsersExport(), 'users.xlsx');
    }
}
