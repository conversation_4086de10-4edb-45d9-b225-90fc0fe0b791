<?php

namespace App\Http\Controllers\Admin;

use App\Constants\FinancialMonthStatus;
use Exception;
use Carbon\Carbon;
use App\Constants\UserType;
use Illuminate\Http\Request;
use App\Models\FinancialYear;
use App\Models\FinancialMonth;
use App\Models\ShippingInvoice;
use App\Http\Controllers\Controller;
use App\Constants\ShippingInvoiceStatus;
use App\Models\Expense;
use App\Models\Purchase;
use App\Models\Revenue;
use App\Models\WithdrawRevenue;

class FinancialYearController extends Controller
{
    public function index()
    {
        $data["title"] = __("Manage Financial Months");
        $data["sub_title"] = __("Financial Months");

        $data["financial_years"] = FinancialYear::where("status", 1)
            ->orderBy("status", "desc")->get();

        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $date = Carbon::createFromDate(null, $i, 1); // Create date for each month
            $months[] = [
                "index" => $i,
                'en' => $date->format('F'), // English name
                'ar' => $date->locale('ar')->translatedFormat('F') // Arabic name
            ];
        }
        $data["months"] = $months;
        // $data["financial_months"] = FinancialMonth::whereHas("financialYear", function ($query) {
        //     $query->where("status", 1);
        // })->get();
        $financial_year_id = request()->query("financial_year_id");
        $start_from = request()->query("start_from");
        $end_to = request()->query("end_to");

        $financial_months = FinancialMonth::when($financial_year_id, function ($query) use ($financial_year_id) {
            $query->where("financial_year_id", $financial_year_id);
        })
            ->when($start_from, function ($query) use ($start_from) {
                $query->whereMonth("start_from", ">=", $start_from);
            })
            ->when($end_to, function ($query) use ($end_to) {
                $query->whereMonth("end_to", "<=", $end_to);
            })
            ->get();
        $data["financial_months"] = $financial_months;
        return view('admin.financial_years.index', $data);
    }
    public function list(Request $request)
    {
        $data = [];
        try {
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "asc";

            $financial_year_id = $request->financial_year_id;
            $start_from = $request->start_from;
            $end_to = $request->end_to;

            $query = FinancialMonth::when($financial_year_id, function ($query) use ($financial_year_id) {
                $query->where("financial_year_id", $financial_year_id);
            })
                ->when($start_from, function ($query) use ($start_from) {
                    $query->whereMonth("start_from", ">=", $start_from);
                })
                ->when($end_to, function ($query) use ($end_to) {
                    $query->whereMonth("end_to", "<=", $end_to);
                })
                ->orderBy($orderBy, $direction)
                ->get();
            $data["financial_months"] = $query;
            $view = view("admin.financial_years.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    public function loadMonthFinancialState(Request $request)
    {

        try {
            $financial_year_id = $request->financial_year_id;
            $month = $request->month;
            $start_balance = 0;
            $total_expenses = 0;
            $total_revenues = 0;
            $withdrawn_revenues = 0;
            $end_balance = 0;

            $financial_month = FinancialMonth::where("financial_year_id", $financial_year_id)
                ->whereMonth("start_from", $month)
                ->first();

            if ($month == 1) {
                $start_balance = $financial_month->start_balance;
            } else {
                $start_balance = $request->start_balance;
            }
            if ($financial_month->status == FinancialMonthStatus::Closed) {
                return response()->json([
                    "month_id" => $financial_month->id,
                    "start_balance" => $start_balance,
                    "total_expenses" => $total_expenses,
                    "total_revenues" => $total_revenues,
                    "withdrawn_revenues" => $withdrawn_revenues,
                    "end_balance" => $end_balance,
                ]);
            }
            $broker_invoices = ShippingInvoice::where("status", ShippingInvoiceStatus::Approved->value)
                ->whereHas("user", function ($q) {
                    $q->where("type", UserType::Broker->value);
                })->whereMonth("shipping_invoices.created_at", $month)
                ->join('shipping_expenses', 'shipping_invoices.id', '=', 'shipping_expenses.shipping_invoice_id')
                ->sum('shipping_expenses.amount');

            $expenses = Expense::whereMonth("date", $month)->sum("amount");
            $purchases = Purchase::whereMonth("date", $month)->sum("amount");
            $total_expenses += $broker_invoices + $expenses + $purchases;

            $client_invoices = ShippingInvoice::where("status", ShippingInvoiceStatus::Approved->value)
                ->whereHas("user", function ($q) {
                    $q->where("type", UserType::Client->value);
                })->whereMonth("shipping_invoices.created_at", $month)
                ->join('shipping_expenses', 'shipping_invoices.id', '=', 'shipping_expenses.shipping_invoice_id')
                ->sum('shipping_expenses.amount');
            $revenues = Revenue::whereMonth("date", $month)->sum("amount");
            $total_revenues = ($client_invoices - $broker_invoices) + $revenues;
            $withdrawn_revenues = WithdrawRevenue::whereMonth("date", $month)->sum("amount");

            if ($financial_month->status == FinancialMonthStatus::Archived) {
                $end_balance = $start_balance + $total_revenues - $total_expenses;
            }
            return response()->json([
                "month_id" => $financial_month->id,
                "start_balance" => $start_balance,
                "total_expenses" => $total_expenses,
                "total_revenues" => $total_revenues,
                "withdrawn_revenues" => $withdrawn_revenues,
                "end_balance" => $end_balance,
            ]);
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
