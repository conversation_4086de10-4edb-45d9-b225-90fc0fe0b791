<?php

namespace App\Constants;

enum CustomShippingStatus: int
{
    case ASSIGNED = 0;  //Automatic
    case BillOfLadingProcessing = 1; //Automatic
    case ThroughShipping = 2; //Automatic
    case ShipmentNotReceivedDelayed = 3; //Manual
    case BillOfLadingAknowledged = 4; //Manual
    case PARTIAL_DELIVERED = 5; //Manual
    case FULL_DELIVERED = 6; //Manually
    case CustomsClearanceInitiated = 7; //Automatic
    case CustomsClearanceCompleted = 8; //Manual
    case UnderCustomsExamination = 9; //Automatic
    case INSPECTED = 10; //Manual
    case ShipmentInTransitFromCustoms = 11; //Automatic
    case RELEASED = 12; //Manual
    case Distributed = 13; //Manual

    case Expo_ThroughShipping = 14;
    case Expo_Received = 15;
    case Expo_ClearanceInitiated = 16;
    case Expo_ClearanceCompleted = 17;
    case Expo_ThroughExporting = 18;
    case Expo_Exported = 19;

        // Filter Purpose
    case UnderClearance = 20;



    public static function availableTypes(): array
    {
        return  [
            "Assigned" => self::ASSIGNED,
            "Through Shipping" => self::ThroughShipping,
            "Shipment Not Received & Delayed" => self::ShipmentNotReceivedDelayed,
            "Partial Delivered" => self::PARTIAL_DELIVERED,
            "Full Delivered" =>  self::FULL_DELIVERED,
            "Inspected" => self::INSPECTED,
            "Released" => self::RELEASED,
            "Distributed" => self::Distributed,
            "UnderClearance" => self::UnderClearance
        ];
    }
    public static function importAvailableTypes(): array
    {
        return  [
            "Assigned" => self::ASSIGNED,
            "Through Shipping" => self::ThroughShipping,
            "Shipment Not Received & Delayed" => self::ShipmentNotReceivedDelayed,
            "Partial Delivered" => self::PARTIAL_DELIVERED,
            "Full Delivered" =>  self::FULL_DELIVERED,
            "Inspected" => self::INSPECTED,
            "Released" => self::RELEASED,
            "Distributed" => self::Distributed,
        ];
    }
    public static function exportAvailableTypes(): array
    {
        return  [
            "Shippment Being Received" => self::Expo_ThroughShipping,
            "Shippment Received Successfully" => self::Expo_Received,
            "Shipment In Inspection and Clearance" => self::Expo_ClearanceInitiated,
            "Shipment Clearance Completed" => self::Expo_ClearanceCompleted,
            "Shipment Through Exporting" =>  self::Expo_ThroughExporting,
            "Shipment Exported Successfully" => self::Expo_Exported,
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::ASSIGNED => "Assigned",
            self::ThroughShipping => "Through Shipping",
            self::PARTIAL_DELIVERED => "Partial Delivered",
            self::FULL_DELIVERED => "Full Delivered",
            self::INSPECTED => "Inspected",
            self::RELEASED => "Released",
            self::Distributed => "Distributed",
            self::BillOfLadingProcessing => "Bill Of Lading Processing",
            self::ShipmentNotReceivedDelayed => "Shipment Is Not Received / Delayed",
            self::BillOfLadingAknowledged => "Bill Of Lading Aknowledged",
            self::CustomsClearanceInitiated => "Customs Clearance Is Initiated",
            self::CustomsClearanceCompleted => "Customs Clearance Is Completed",
            self::UnderCustomsExamination => "Shipment Is Under Customs Examination",
            self::ShipmentInTransitFromCustoms => "Shipment Is In Transit From Customs",
            self::Expo_ThroughShipping => "Shippment Being Received",
            self::Expo_Received => "Shippment Received Successfully",
            self::Expo_ClearanceInitiated => "Shipment In Inspection and Clearance",
            self::Expo_ClearanceCompleted => "Shipment Clearance Completed",
            self::Expo_ThroughExporting => "Shipment Through Exporting",
            self::Expo_Exported => "Shipment Exported Successfully",
        };
    }
}
