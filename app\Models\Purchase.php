<?php

namespace App\Models;

use App\Models\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Purchase extends Model
{
    use Auditable;
    protected $fillable = [
        "finance_category_id",
        "date",
        "amount",
        "description",
        "created_by",
        "updated_by"
    ];

    public function financeCategory(): BelongsTo
    {
        return $this->belongsTo(FinanceCategory::class);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }
    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachmentable');
    }

    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.purchases.edit");
    }
}
