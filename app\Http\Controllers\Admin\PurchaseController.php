<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Purchase;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\FinanceCategory;
use App\Services\PurchaseService;
use Illuminate\Support\Facades\DB;
use App\Events\NewExpenseAddedEvent;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Purchase\CreateRequest;
use App\Http\Requests\Admin\Purchase\UpdateRequest;

class PurchaseController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new PurchaseService();
        $this->middleware('is_able:Create Purchase')->only(['create', 'store']);
        $this->middleware('is_able:View Purchases')->only(['index', 'view']);
        $this->middleware('is_able:Update Purchase')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Purchase')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Purchases");

        $categories = DB::table("finance_categories AS c")
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_purchases FROM purchases GROUP BY finance_category_id) AS p"), function ($join) {
                $join->on("p.finance_category_id", "=", "c.id");
            })
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_revenues FROM revenues GROUP BY finance_category_id) AS r"), function ($join) {
                $join->on("r.finance_category_id", "=", "c.id");
            })
            ->select(
                "c.id",
                "c.name",
                DB::raw('COALESCE(p.total_purchases, 0) as total_purchases'),
                DB::raw('COALESCE(r.total_revenues, 0) as total_revenues')
            )
            ->get();
        $financeCategories = FinanceCategory::all();
        $current_type = request()->query("category");

        $data["categories"] = $categories;
        $data["financeCategories"] = $financeCategories;
        $data["current_type"] = $current_type;

        return view("admin.purchases.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";
            $selectedType = $request->SelectedType;
            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Purchase::with("financeCategory", "attachments")
                ->when($selectedType && $selectedType != "all", function ($query) use ($selectedType) {
                    $query->where("finance_category_id", $selectedType);
                })
                ->when($search, function ($query) use ($search) {
                    $query->whereHas("financeCategory", function ($q) use ($search) {
                        $q->where("name", "LIKE", "%" . $search . "%");
                    });
                });
            if ($orderBy == "finance_category") {
                $query = $query
                    ->leftJoin('finance_categories AS f', 'f.id', '=', 'purchases.finance_category_id')
                    ->orderBy('p.name', $direction)->select("purchases.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.purchases.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Purchases");
        $data["sub_title"] = __("Add New Purchase");
        $data["finance_categories"] = FinanceCategory::where("status", 1)->get();
        return view('admin.purchases.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = Purchase::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("Purchases");
        $data["sub_title"] = __("Update Purchase");
        $data["finance_categories"] = FinanceCategory::where("status", 1)->get();
        $data["purchase"] = Purchase::with("attachments")->findOrFail($id);
        return view('admin.purchases.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $purchase = Purchase::findOrFail($id);
            Purchase::destroy($id);

            NewExpenseAddedEvent::dispatch(-$purchase->amount);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
    public function exportAllToPdf()
    {
        $logo = "";
        $current_type = request()->query("category");
        $purchases = Purchase::query()->with("financeCategory")
            ->when($current_type && $current_type != "all", function ($query) use ($current_type) {
                $query->where("finance_category_id", $current_type);
            })
            ->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.purchases.export_pdf', [
            'purchases' => $purchases,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_purchases.pdf", 'D');
    }
}
