<?php

namespace App\Models;

use App\Constants\Status;
use App\Models\Traits\Auditable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Advance extends Model
{
    use Auditable;
    protected $fillable = [
        "to_user_id",
        "from_user_id",
        "amount",
        "date",
        "status",
        "notes",
        "approved_at",
        "current_balance",
        "created_by",
        "updated_by"
    ];
    protected $casts = [
        "status" => Status::class
    ];
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, "to_user_id");
    }
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, "from_user_id");
    }

    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, "attachmentable");
    }

    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.advances.edit");
    }
}
