<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PermissionsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('permissions')->delete();
        DB::table('roles')->delete();

        Artisan::call("cache:clear");
        $permissions = [
            "Custom Shippings" => [
                "Create Custom Shipping",
                "View All Custom Shippings",
                "View Related Custom Shippings",
                "View Created By Custom Shippings",
                "Assign Custom Shipping To Broker",
                "Assign Custom Shipping To Client",
                "Upload Attachment In Custom Shipping",
                "View All Custom Shippings Status",
                "View Related Custom Shippings Status",
                "View Created By Custom Shippings Status",
                "Change Custom Shipping Status",
                "Set Shipment As Delivered",
                "Update Custom Shippings",
                "Update Related Custom Shippings",
                "Update Created By Custom Shippings",
                "Delete Custom Shippings",
                "Delete Related Custom Shippings",
                "Delete Created By Custom Shippings",
            ],
            "Shipping Invoices" => [
                "Create Shipping Invoice",
                "Update Shipping Invoice",
                "Change Shipping Invoice Status",
                "View All Shipping Invoices",
                "View Related Shipping Invoices",
                "Delete Shipping Invoice",
            ],
            "Advances" => [
                "Create Advance",
                "View Advances",
                "View Related Advances",
                "Change Advance Status",
                "Delete Advance",
                "Update Advance",
            ],
            "Dashboard Cards" => [
                "Print Account Statement",
                "Search HS Sheet",
                "Track Custom Shipping",
                "Total Custom Shippings Count",
                "Total Delivered Custom Shippings Count",
                "Total Custom Shippings Under Clearance Count",
                "Total Released Custom Shippings Count",
                "Total Received Advances Amount",
                "Total Paid Advances Amount",
                "Total Client Debits Amount",
                "Total Worker Dues Amount"
            ],
            "Clients" => [
                "Create Client",
                "View Clients",
                "Delete Client",
                "Update Client",

                "Create Company",
                "View Companies",
                "Delete Company",
                "Update Company",

                "Create Client Debit",
                "View Client Debits",
                "View Related Client Debits",
                "Delete Client Debit",
                "Update Client Debit",
            ],
            "Worker Dues" => [
                "Create Worker Due",
                "View Worker Dues",
                "View Related Worker Dues",
                "Delete Worker Due",
                "Update Worker Due",
            ],
            "Shipping Expense Types" => [
                "Create Shipping Expense Type",
                "View Shipping Expense Types",
                "Delete Shipping Expense Type",
                "Update Shipping Expense Type",
            ],
            "Documents" => [
                "Create Document",
                "View Documents",
                "Delete Document",
                "Update Document",
            ],
            "Expenses" => [
                "Create Expense",
                "View Expenses",
                "Delete Expense",
                "Update Expense",
            ],
            "Withdraw Revenues" => [
                "Create Withdraw Revenue",
                "View Withdraw Revenues",
                "Delete Withdraw Revenue",
                "Update Withdraw Revenue",
            ],
            "Purchases" => [
                "Create Purchase",
                "View Purchases",
                "Delete Purchase",
                "Update Purchase",
            ],
            "Revenues" => [
                "Create Revenue",
                "View Revenues",
                "Delete Revenue",
                "Update Revenue",
            ],
            "Users" => [
                "Create User",
                "View Users",
                "Delete User",
                "Update User",
                "Assign Permissions",
                "Create Role",
                "View Roles",
                "Delete Role",
                "Update Role",
            ],
            "General" => [
                "View Hs Sheet",
                "View General Settings",
                "Update General Settings",
                "View Intialization Settings",
                "Update Intialization Settings",
            ]
        ];

        foreach ($permissions as $group => $array) {
            foreach ($array as $permission) {
                $exists =  Permission::where('name', $permission)->first();
                if (!$exists) {
                    Permission::create([
                        "group" => $group,
                        "name" => $permission
                    ]);
                }
            }
        }
    }
}
