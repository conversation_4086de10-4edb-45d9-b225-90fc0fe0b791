<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_shipping_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId("custom_shipping_id")->constrained("custom_shippings")->onDelete("cascade");

            $table->integer("status")->nullable();
            $table->text("description")->nullable();
            $table->integer("packages_count")->nullable();
            $table->date("date")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_shipping_statuses');
    }
};
