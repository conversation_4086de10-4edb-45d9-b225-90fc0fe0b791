<?php

namespace App\Constants;

enum AttachmentVisibleStatus: int
{
    case Client = 1;
    case Employee = 2;
    case Both = 3;

    public static function availableTypes(): array
    {
        return  [
            "Client" => self::Client,
            "Employee" => self::Employee,
            "Both" => self::Both,
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::Client => "Client",
            self::Employee => "Employee",
            self::Both => "Both",
        };
    }
}
