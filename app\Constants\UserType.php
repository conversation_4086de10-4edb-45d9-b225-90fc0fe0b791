<?php

namespace App\Constants;

enum UserType: int
{
    case Employee = 1;
    case Client = 2;
    case SubClient = 3;
    case SystemEmployee = 4;
    case Broker = 5;

    public static function availableTypes(): array
    {
        return  [
            "Employee" => self::Employee,
            "Client" => self::Client,
            "SubClient" => self::SubClient,
            "SystemEmployee" => self::SystemEmployee
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::Employee => "Employee",
            self::Client => "Client",
            self::SubClient => "SubClient",
            self::SystemEmployee => "SystemEmployee",
            self::Broker => "Broker"
        };
    }
    /**
     * Compare the enum with a value.
     *
     * @param int|UserType $value
     * @return bool
     */
    public function equals(int|UserType $value): bool
    {
        return $value instanceof self ? $this === $value : $this->value === $value;
    }
}
