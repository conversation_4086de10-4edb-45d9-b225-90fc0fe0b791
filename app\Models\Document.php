<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Document extends Model
{
    use Auditable;
    protected $fillable = [
        "name",
        "name_ar",
        "name_en",
        "document_type_id",
        "end_date",
        "notify_before",
        "description",
        "created_by",
        "updated_by"
    ];

    public function documentType(): BelongsTo
    {
        return $this->belongsTo(DocumentType::class);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }
    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachmentable');
    }

    public function remainingDays(): Attribute
    {
        return  Attribute::make(
            get: function () {
                $end_date = Carbon::parse($this->attributes["end_date"]);
                $today = Carbon::now()->startOfDay();
                $status = "";
                if ($end_date <= $today) {
                    $status = __("Expired Since");
                } else {
                    $status = __("Left");
                }
                $diff = $today->diffInDays($end_date, true);
                return "{$status} {$diff} " . __("Day");
            }
        );
    }
    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }

    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.documents.edit");
    }
}
