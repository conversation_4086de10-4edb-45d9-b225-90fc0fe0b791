<?php

namespace App\Services;

use App\Models\Document;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Constants\AttachmentVisibleStatus;

class DocumentService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            $document = Document::create($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/documents/$document->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $document->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $document = Document::findOrFail($id);
            $document->update($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/documents/$document->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $document->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
