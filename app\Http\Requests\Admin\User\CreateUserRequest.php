<?php

namespace App\Http\Requests\Admin\User;

use App\Constants\CrudMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "name_ar" => "required|max:100",
            "name_en" => "required|max:100",
            "username" => "required|unique:users,username",
            "email" => "required|email|unique:users,email",
            "password" => "required|min:6|max:20|confirmed",
            "password_confirmation" => "required",
            "phone" => "sometimes",
            "address" => "sometimes",
            "roles" => "sometimes|array",
            "IsSuperAdmin" => "sometimes",
            "status" => "required",
            "type" => "sometimes",
            "client_id" => "sometimes",
        ];
    }


    public function messages(): array
    {
        return [
            "username.required" => CrudMessage::isRequired(__("username")),
            "username.unique" => CrudMessage::alreadyExists(__("username")),
            "name_ar.required" => CrudMessage::isRequired(__("Arabic Name")),
            "name_ar.max" => CrudMessage::maxLengthCharacters(100),
            "name_en.required" => CrudMessage::isRequired(__("English Name")),
            "name_en.max" => CrudMessage::maxLengthCharacters(100),
            "status.required" => __("this field is required"),
            "email.required" => __("this field is required"),
            "email.unique" => __("email already exists"),
            "email.email" => __("email is not valid"),
            "password.required" => __("this field is required"),
            "password.confirmed" => __("Password does not match"),
            "password.min" => __("not allowed less than 6 characters"),
            "password.max" => __("not allowed more than 20 characters"),
            "password_confirmation.required" => __("this field is required"),
        ];
    }
}
