<?php

namespace App\Console\Commands;

use App\Models\HsSheet;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class SeedExcelSheetToDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-hs-sheet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sheet_exists = public_path("storage/hs_sheet.xlsx");
        $sheet = storage_path("hs_sheet.xlsx");
        $data_list = [];
        $data = Excel::toCollection(null, $sheet_exists);
        $list = $data->first();
        $count = $list->count();
        foreach ($list as $key => $value) {
            if ($key != 0) {
                $restriction_list = array_filter(explode(" -------------------- ", @$value[6]));
                $prohibition_list = array_filter(explode(" -------------------- ", @$value[7]));
                $el["hs_code"] = @$value[0];
                $el["duty"] = @$value[1];
                $el["name_en"] = @$value[2];
                $el["name_ar"] = @$value[3];
                $el["description"] = @$value[4];
                $el["unit"] = @$value[5];
                $el["restriction_import"] = json_encode($restriction_list);
                $el["prohibition_import"] = json_encode($prohibition_list);

                array_push($data_list, $el);
            }
        }

        HsSheet::insert($data_list);
        $this->info("sheet has been seeded into database successfully");
    }
}
