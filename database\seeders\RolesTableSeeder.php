<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class RolesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $shipping_borker = Role::firstOrCreate([
            "name" => "Broker",
            "guard_name" => "web"
        ]);
        $shipping_borker->syncPermissions([
            "Create Custom Shipping",
            "View Related Custom Shippings",
            "View Created By Custom Shippings",
            "Upload Attachment In Custom Shipping",
            "Assign Custom Shipping To Client",
            "View Hs Sheet",
            "View Created By Custom Shippings Status",
            "View Related Custom Shippings Status",
            "Change Custom Shipping Status",
            "Update Created By Custom Shippings",
            "View Related Worker Dues",
            "View Related Advances",
            "View Related Shipping Invoices",
            "Update Shipping Invoice",
            "Print Account Statement",
            "Search HS Sheet",
            "Track Custom Shipping",
            "Total Custom Shippings Count",
            "Total Delivered Custom Shippings Count",
            "Total Custom Shippings Under Clearance Count",
            "Total Released Custom Shippings Count",
            "Total Received Advances Amount",
            "Total Worker Dues Amount"
        ]);

        $shipping_borker = Role::firstOrCreate([
            "name" => "System Employee",
            "guard_name" => "web"
        ]);
        $shipping_borker->syncPermissions([
            "View Hs Sheet",
            "Create Custom Shipping",
            "View Created By Custom Shippings",
            "Assign Custom Shipping To Broker",
            "Assign Custom Shipping To Client",
            "Upload Attachment In Custom Shipping",
            "View Created By Custom Shippings Status",
            "Update Created By Custom Shippings",
            "Delete Created By Custom Shippings",
            "Search HS Sheet",
            "Track Custom Shipping",
            "Total Custom Shippings Count",
            "Total Delivered Custom Shippings Count",
            "Total Custom Shippings Under Clearance Count",
            "Total Released Custom Shippings Count",
        ]);
        $client = Role::firstOrCreate([
            "name" => "Client",
            "guard_name" => "web"
        ]);
        $client->syncPermissions([
            "View Hs Sheet",
            "View Related Custom Shippings",
            "View Related Custom Shippings Status",
            "Set Shipment As Delivered",
            "View Related Advances",
            "View Related Shipping Invoices",
            "View Related Client Debits",
            "Create Advance",
            "Update Advance",
            "View Documents",
            "Print Account Statement",
            "Search HS Sheet",
            "Track Custom Shipping",
            "Total Custom Shippings Count",
            "Total Delivered Custom Shippings Count",
            "Total Custom Shippings Under Clearance Count",
            "Total Released Custom Shippings Count",
            "Total Paid Advances Amount",
            "Total Client Debits Amount",
        ]);
        $subClient = Role::firstOrCreate([
            "name" => "SubClient",
            "guard_name" => "web"
        ]);
        $subClient->syncPermissions([
            "View Hs Sheet",
            "View Related Custom Shippings",
            "View Related Custom Shippings Status",
            "Set Shipment As Delivered",
            "View Related Advances",
            "View Related Shipping Invoices",
            "View Related Client Debits",
            "View Documents",
            "Print Account Statement",
            "Search HS Sheet",
            "Track Custom Shipping",
            "Total Custom Shippings Count",
            "Total Delivered Custom Shippings Count",
            "Total Custom Shippings Under Clearance Count",
            "Total Released Custom Shippings Count",
            "Total Paid Advances Amount",
            "Total Client Debits Amount",
        ]);
    }
}
