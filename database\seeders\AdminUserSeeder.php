<?php

namespace Database\Seeders;

use App\Constants\UserType;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('IsSuperAdmin', 1)->first();
        if (!isset($admin)) {
            User::create([
                "username" => "admin",
                "name" => "SuperAdmin",
                "name_ar" => "SuperAdmin",
                "name_en" => "SuperAdmin",
                "email" => "<EMAIL>",
                "password" => Hash::make('123456'),
                "status" => "Active",
                "type" => UserType::Employee,
                "IsSuperAdmin" => true
            ]);
        }
    }
}
