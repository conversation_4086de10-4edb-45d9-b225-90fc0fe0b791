<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_shippings', function (Blueprint $table) {
            $table->id();
            $table->string("policy_number")->nullable();
            $table->string("code")->nullable();
            $table->integer("type")->nullable(); //Internal , External
            $table->integer("shipping_type")->nullable(); //Imported , Exported
            $table->date("date")->nullable();
            $table->string("client_name")->nullable();
            $table->foreignId("client_id")->nullable()->constrained("clients")->onDelete("cascade");
            $table->integer("packages_count")->nullable();
            $table->integer("delivered_packages_count")->nullable();
            $table->integer("remained_packages_count")->nullable();
            $table->integer("units_count")->nullable();
            $table->decimal("total_weight", 10, 2)->nullable();
            $table->decimal("total_size", 10, 2)->nullable();

            $table->unsignedBigInteger('assigned_to')->index()->nullable();
            $table->foreign('assigned_to')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');

            $table->foreignId("from_country_id")->nullable()->constrained("countries")->nullOnDelete();
            $table->foreignId("country_id")->nullable()->constrained("countries")->nullOnDelete();
            $table->integer("delivery_method")->nullable();
            $table->foreignId("air_port_id")->nullable()->constrained("air_ports")->nullOnDelete();
            $table->foreignId("harbor_id")->nullable()->constrained("harbors")->nullOnDelete();
            $table->boolean("has_subsequents")->nullable();
            $table->foreignId("derived_from")->nullable()->constrained("custom_shippings")->nullOnDelete();
            $table->integer("status")->nullable();
            $table->date("invoice_date")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_shippings');
    }
};
