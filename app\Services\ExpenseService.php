<?php

namespace App\Services;

use App\Models\Expense;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Constants\AttachmentVisibleStatus;
use App\Events\NewExpenseAddedEvent;

class ExpenseService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            $expense = Expense::create($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/expenses/$expense->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $expense->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }

            NewExpenseAddedEvent::dispatch($expense->amount);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $expense = Expense::findOrFail($id);
            $new_amount = $data["amount"];
            $amount_diff = $new_amount -  $expense->amount;
            $expense->update($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/expenses/$expense->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $expense->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            NewExpenseAddedEvent::dispatch($amount_diff);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
