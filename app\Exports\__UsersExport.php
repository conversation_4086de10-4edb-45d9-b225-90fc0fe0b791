<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Support\Facades\Date;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class __UsersExport implements FromQuery, WithHeadings
{
    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Phone',
            'Address',
            'Roles',
            'Status',
            'Created At'
        ];
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function map($user): array
    {
        return [
            $user->name,
            $user->email,
            $user->phone,
            $user->address,
            $user->roles->pluck('name')->implode(' - '),
            $user->status,
            Date::dateTimeToExcel($user->created_at),
        ];
    }
    public function query()
    {
        return User::where("type", "employee")
            ->where('id', '!=', auth("web")->id())
            ->where("IsSuperAdmin", "!=", 1);
    }
}
