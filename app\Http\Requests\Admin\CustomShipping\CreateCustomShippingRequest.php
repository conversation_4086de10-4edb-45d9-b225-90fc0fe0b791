<?php

namespace App\Http\Requests\Admin\CustomShipping;

use App\Constants\CrudMessage;
use App\Constants\DeliveryMethod;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateCustomShippingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "policy_number" => "required|max:200",
            "code" => "nullable|max:200",
            "type" => "nullable",
            "shipping_type" => "nullable",
            "delivery_method" => "nullable",
            "date" => "required",
            "country_id" => "required",
            "from_country_id" => "required",
            "harbor_id" => "nullable",
            "air_port_id" => "nullable",
            "client_id" => "nullable",
            "company_id" => "nullable",
            "packages_count" => "required",
            "units_count" => "nullable",
            "total_weight" => "required",
            "total_size" => "nullable",
            "assigned_to" => "nullable",
            "status" => "nullable",
            "files_list" => "sometimes|array",
            "files_list.*.attachment" => "sometimes",
            "files_list.*.visible_to" => "sometimes",
        ];
    }


    public function messages(): array
    {
        return [
            "policy_number.required" => __("policy number is required"),
            "type.required" => __("type is required"),
            "shipping_type.required" =>  CrudMessage::isRequired(__("Shipping Type")),
            "packages_count.required" => __("packages count is required"),
            "total_weight.required" => __("total weight is required"),
            "delivery_method.required" => CrudMessage::isRequired(__("Delivery Method")),
            "country_id.required" => CrudMessage::isRequired(__("Country")),
            "from_country_id.required" => CrudMessage::isRequired(__("Exporting Country")),
            "date.required" => CrudMessage::isRequired(__("Date")),
            "air_port_id.required_id" => CrudMessage::isRequired(__("Airport")),
            "harbor_id.required" => CrudMessage::isRequired(__("Harbor")),
        ];
    }
}
