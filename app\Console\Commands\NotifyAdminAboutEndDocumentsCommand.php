<?php

namespace App\Console\Commands;

use App\Models\Document;
use App\Models\Notification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class NotifyAdminAboutEndDocumentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:end-docs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify Admin About Nearly End Documents';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $locale =  session()->get("locale");
        app()->setLocale($locale);
        $documents = Document::whereDate('end_date', '>', now()->toDateString())->get();
        $admin = User::where("IsSuperAdmin", 1)->first();
        foreach ($documents as $doc) {
            $message = " نود تذكيرك بقرب موعد إنتهاء المستند " . $doc->doc_name . " فى الموعد " . $doc->end_date;
            $today = now()->today();
            $date = Carbon::parse($doc->end_date)->subDays($doc->notify_before);
            if ($today >= $date) {

                $existed_notification = Notification::where("reference_type", get_class($doc))
                    ->where("reference_id", $doc->id)
                    ->first();

                if (!isset($existed_notification)) {
                    //create notification
                    Notification::create([
                        "user_id" => $admin->id,
                        "from_user_id" => $admin->id,
                        "title" => $doc->name,
                        "content" => $message,
                        "route" => "admin.documents.edit",
                        "routeParams" => json_encode($doc->id),
                        "reference_id" => $doc->id,
                        "reference_type" => get_class($doc)
                    ]);
                }
            }
        }
    }
}
