<?php

namespace App\Http\Requests\Admin;

use App\Constants\CrudMessage;
use App\Models\AuthenticationLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;

class LoginRequest extends FormRequest
{

    private const SUCCESS = 1;
    private const FAILED = 2;
    private const SUSPENDED = 3;
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'max:100'],
            'password' => ['required', 'string', 'min:6', 'max:100'],
            "remember" => "nullable|boolean"
        ];
    }
    public function messages(): array
    {
        return [
            "password.min" => CrudMessage::isNotValid("password"),
            "password.max" => CrudMessage::isNotValid("password"),
            "email.required" => CrudMessage::isRequired("email"),
            "email.max" => CrudMessage::isNotValid("email"),
            "password.required" => CrudMessage::isRequired("password")
        ];
    }

    public function authenticate()
    {
        $this->ensureIsNotRateLimited();

        $user = $this->checkUserStatus();

        $credentials = ['email' => $user->email, 'password' => $this->get('password')];

        $loginStatus = $this->verifyUserPassword($user);

        if ($loginStatus == self::SUCCESS) {

            Auth::attempt($credentials, $this->boolean('remember'));
        } elseif ($loginStatus == self::FAILED) {

            RateLimiter::hit($this->throttleKey());

            throw  ValidationException::withMessages([
                'error' => __("Invalid Credentials")
            ]);
        } else {

            RateLimiter::hit($this->throttleKey());

            throw  ValidationException::withMessages([
                'error' => __("To Protect Your Data, Your Account Is Suspended Temporarily")
            ]);
        }

        RateLimiter::clear($this->throttleKey());
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    private function checkUserStatus()
    {
        $user = User::where(['email' => $this->get("email")])
            ->orWhereRaw('LOWER(username) = LOWER(?)', [Str::lower($this->get("email"))])
            ->first();
        if ($user == null) {
            throw ValidationException::withMessages([
                'error' => __("Invalid Credentials")
            ]);
        }

        if ($user->status == "Suspended") {
            //check suspended at
            $suspended_at = Carbon::parse($user->suspended_at)->addHour();
            if ($suspended_at <= now()) {
                $user->failed_login_attempts = 0;
                $user->status = "Active";
                $user->save();
            } else {
                throw ValidationException::withMessages([
                    'error' => __("To Protect Your Data, Your Account Is Suspended Temporarily")
                ]);
            }
        } else if ($user->status == "Blocked") {
            throw ValidationException::withMessages([
                'error' => __("Your Account Has Been Blocked")
            ]);
        }
        return $user;
    }

    private function verifyUserPassword(User $user)
    {
        $password = $this->get("password");
        $verifyStatus = Hash::check($password, $user->password);
        $loginStatus = "";
        if (!$verifyStatus) {
            $user->failed_login_attempts = ($user->failed_login_attempts ?? 0) + 1;
            $user->save();
            if ($user->failed_login_attempts == 5) {
                $user->suspended_at = now();
                $user->status = "Suspended";
                $user->save();

                $loginStatus = self::SUSPENDED;
            }
            $loginStatus = self::FAILED;
        } else {
            $user->failed_login_attempts = 0;
            $user->save();
            $loginStatus = self::SUCCESS;
        }
        $this->createAuthenticationLog($user->id, $verifyStatus ? "Success" : "Failed");

        return $loginStatus;
    }

    private function createAuthenticationLog($user_id, $status)
    {

        $log = AuthenticationLog::create([
            'ip_address' => $this->ip(),
            'browser' =>  $this->header('User-Agent'),
            'user_id' => $user_id,
            'payload' => json_encode($this->except(['password'])),
            'status' => $status
        ]);
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }
    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('email')) . '|' . $this->ip());
    }
}
