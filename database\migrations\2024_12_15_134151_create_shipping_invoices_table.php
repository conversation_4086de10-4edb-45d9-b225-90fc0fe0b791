<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_invoices', function (Blueprint $table) {
            $table->id();
            $table->string("invoice_number")->nullable();
            $table->foreignId("user_id")->nullable()->constrained("users")->onDelete('cascade');
            $table->foreignId("custom_shipping_id")->nullable()->constrained("custom_shippings")->onDelete('cascade');
            $table->integer("status")->nullable();
            $table->text("notes")->nullable();
            $table->decimal("current_balance", 10, 2)->nullable();
            $table->date("date")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_invoices');
    }
};
