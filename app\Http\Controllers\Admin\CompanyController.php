<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\Client;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\CompanyService;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Company\CreateRequest;
use App\Http\Requests\Admin\Company\UpdateRequest;
use App\Models\Company;

class CompanyController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new CompanyService();
        $this->middleware('is_able:Create Company')->only(['create', 'store']);
        $this->middleware('is_able:View Companies')->only(['index', 'view']);
        $this->middleware('is_able:Update Company')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Company')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Companies");
        $data["sub_title"] = __("Companies List");
        return view('admin.companies.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = Company::with("client")->when($search, function ($query) use ($search) {
                $query
                    ->where("name_ar", "LIKE", "%" . $search . "%")
                    ->orWhere("name_en", "LIKE", "%" . $search . "%")
                    ->orWhereHas("client", function ($q) use ($search) {
                        $q->where("name_ar", "LIKE", "%" . $search . "%")
                            ->orWhere("name_en", "LIKE", "%" . $search . "%")
                            ->orWhere("phone", "LIKE", "%" . $search . "%");
                    });
            });
            if ($orderBy == "client_name") {
                $query = $query
                    ->leftJoin('clients as c', 'c.id', '=', 'companies.client_id')
                    ->orderBy(app()->getLocale() == "ar" ? 'c.name_ar' : "c.name_en", $direction)->select("companies.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $clients = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $clients;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.companies.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Companies");
        $data["sub_title"] = __("Add New Company");
        $data["clients"] = Client::all();
        return view('admin.companies.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('admin.show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $data["title"] = __("Company");
            $data["sub_title"] = __("Update Company");

            $data["clients"] = Client::all();
            $data["company"] = Company::findOrFail($id);

            return view('admin.companies.edit', $data);
        } catch (Exception $e) {
            session()->flash("error", $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            Company::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = "";
        $clients = Company::query()->with("client")->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.companies.export_pdf', [
            'clients' => $clients,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_clients.pdf", 'D');
    }
}
