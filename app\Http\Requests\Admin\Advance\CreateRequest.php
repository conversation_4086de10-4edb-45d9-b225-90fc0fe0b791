<?php

namespace App\Http\Requests\Admin\Advance;

use App\Constants\CrudMessage;
use App\Constants\UserType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "from_user_id" => "required_if:type," . UserType::Client->value,
            "to_user_id" => "required_if:type," . UserType::Broker->value,
            "amount" => "required",
            "date" => "nullable",
            "type" => "required",
            "status" => "required",
            "notes" => "nullable",
            "created_by" => "sometimes",
            "attachments" => "nullable|array"
        ];
    }


    public function messages(): array
    {
        return [
            "from_user_id.required" => CrudMessage::isRequired(__("User")),
            "to_user_id.required" => CrudMessage::isRequired(__("User")),
            "amount.required" => CrudMessage::isRequired(__("amount")),

        ];
    }
    protected function prepareForValidation()
    {
        $this->merge([
            "created_by" => auth("web")->id()
        ]);
    }
}
