<?php

namespace App\Constants;

enum  DeliveryMethod: int
{
    case ByAir = 1;
    case BySea = 2;
    case ByLand = 3;

    public static function availableTypes(): array
    {
        return  [
            "By Air" => self::ByAir,
            "By Sea" => self::BySea,
            "By Land" => self::ByLand,
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::ByAir => "By Air",
            self::BySea => "By Sea",
            self::ByLand => "By Land",
        };
    }
}
