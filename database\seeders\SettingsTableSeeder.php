<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                "key" => "site_name",
                "value" => "Expo",
                "type" => "general_setting"
            ],
            [
                "key" => "company_name",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "owner_name",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "email",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "phone",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "other_phone",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "location",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "logo",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "favicon",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "description",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "facebook",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "whatsapp",
                "value" => "",
                "type" => "general_setting"
            ],
            [
                "key" => "google_map",
                "value" => "",
                "type" => "general_setting"
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(["key" => $setting["key"]], [
                "value" => $setting["value"],
                "type" => $setting["type"]
            ]);
        }
        // Setting::insert($settings);
    }
}
