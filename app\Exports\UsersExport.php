<?php

namespace App\Exports;

use App\Models\User;
use Carbon\Traits\Date;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UsersExport implements FromCollection, WithHeadings, WithStyles
{
    public function collection()
    {
        $data = [];
        $users = User::where("type", "employee")
            ->where('id', '!=', auth("web")->id())
            ->where("IsSuperAdmin", "!=", 1)->get();

        foreach ($users as $user) {
            $data[] = [
                "Name" => $user->name,
                "Email" => $user->email,
                "Phone" => $user->phone,
                "Address" => $user->address,
                "Status" => $user->status,
                "Roles" => $user->roles->pluck('name')->implode(' - '),
                "Created At" => $user->created_at->toDateString(),
            ];
        }

        return new Collection($data);
    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Phone',
            'Address',
            'Status',
            'Roles',
            'Created At'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:G1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Style data cells
        $sheet->getStyle('A2:G101')->applyFromArray([
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
        ]);

        return $sheet;
    }
}
