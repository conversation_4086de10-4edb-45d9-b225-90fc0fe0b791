<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class ClientDebit extends Model
{
    protected $fillable = [
        "user_id",
        "amount",
        "created_by",
        "updated_by"
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
