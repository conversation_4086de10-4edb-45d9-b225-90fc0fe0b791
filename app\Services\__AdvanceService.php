<?php

namespace App\Services;

use App\Models\Wallet;
use Exception;
use App\Models\User;
use App\Models\Advance;
use App\Constants\Status;
use App\Models\WorkerDue;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Events\NewExpenseAddedEvent;
use App\Events\NewRevenueAddedEvent;
use App\Constants\AttachmentVisibleStatus;

class __AdvanceService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            $advance = Advance::create([
                "to_user_id" => @$data["to_user_id"],
                "from_user_id" => @$data["from_user_id"],
                "amount" => $data["amount"],
                "date" => @$data["date"],
                "notes" => @$data["notes"],
                "status" => @$data["status"],
                "created_by" => auth("web")->id()
            ]);

            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/advances";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $advance->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => $data["type"] == UserType::Employee->value ? AttachmentVisibleStatus::Employee : AttachmentVisibleStatus::Client
                    ]);
                }
            }
            if ($data["type"] == UserType::Employee->value && $data["status"] == Status::Approved->value) {
                $this->updateWorkerDue($advance->to_user_id, $advance->amount);
                $wallet = $this->updateUserWallet($advance->to_user_id, $advance->amount);
                $this->createNotificationToWorkerAfterAdvancePaid($advance);
                NewExpenseAddedEvent::dispatch($advance->amount); //$advance->amount

                $advance->current_balance = $wallet->amount;
                $advance->save();
            } elseif ($data["type"] == UserType::Client->value && $data["status"] == Status::Approved->value) {
                $this->updateClientDebit($advance->from_user_id, $advance->amount);
                $wallet = $this->updateUserWallet($advance->from_user_id, $advance->amount);
                $this->createNotificationToClientAfterAdvancePaid($advance);
                NewRevenueAddedEvent::dispatch($advance->amount);

                $advance->current_balance = $wallet->amount;
                $advance->save();
            }
            if (is_client() && !is_admin()) {
                $this->createNoificationForAdminAfterClientAddAdvance($advance);
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $advance = Advance::findOrFail($id);
            $amount_difference = $data["amount"] - $advance->amount;
            $old_status = $advance->status;
            $new_status = $data["status"];
            $advance->update(
                [
                    "to_user_id" => @$data["to_user_id"],
                    "from_user_id" => @$data["from_user_id"],
                    "amount" => $data["amount"],
                    "date" => @$data["date"],
                    "notes" => @$data["notes"],
                    "status" => @$data["status"],
                    "updated_by" => auth("web")->id()
                ]
            );
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/advances";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $advance->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => $data["type"] == UserType::Employee->value ? AttachmentVisibleStatus::Employee : AttachmentVisibleStatus::Client
                    ]);
                }
            }
            if ($data["type"] == UserType::Employee->value) {
                //from Pending ==> Approved
                if (Status::Pending->equals($old_status) && Status::Approved->equals($new_status)) {
                    $this->updateWorkerDue($advance->to_user_id, $data["amount"]);
                    $wallet = $this->updateUserWallet($advance->to_user_id, $data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->to_user_id);
                    NewExpenseAddedEvent::dispatch($data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Pending
                else if (Status::Approved->equals($old_status) && Status::Pending->equals($new_status)) {
                    $this->updateWorkerDue($advance->to_user_id, -$data["amount"]);
                    $wallet = $this->updateUserWallet($advance->to_user_id, -$data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->to_user_id);
                    NewExpenseAddedEvent::dispatch(-$data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Rejected
                else if (Status::Approved->equals($old_status) && Status::Rejected->equals($new_status)) {
                    $this->updateWorkerDue($advance->to_user_id, -$data["amount"]);
                    $wallet = $this->updateUserWallet($advance->to_user_id, -$data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->to_user_id);
                    NewExpenseAddedEvent::dispatch(-$data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Approved
                else if (Status::Approved->equals($old_status) && Status::Approved->equals($new_status)) {
                    $this->updateWorkerDue($advance->to_user_id, $amount_difference);
                    $wallet = $this->updateUserWallet($advance->to_user_id, $amount_difference);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->to_user_id);
                    NewExpenseAddedEvent::dispatch($amount_difference);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                }
            } elseif ($data["type"] == UserType::Client->value) {
                //from Pending ==> Approved
                if (Status::Pending->equals($old_status) && Status::Approved->equals($new_status)) {
                    $this->updateClientDebit($advance->from_user_id, $data["amount"]);
                    $wallet = $this->updateUserWallet($advance->from_user_id, $data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->from_user_id);
                    NewRevenueAddedEvent::dispatch($data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Pending
                else if (Status::Approved->equals($old_status) && Status::Pending->equals($new_status)) {
                    $this->updateClientDebit($advance->from_user_id, -$data["amount"]);
                    $wallet = $this->updateUserWallet($advance->from_user_id, -$data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->from_user_id);
                    NewRevenueAddedEvent::dispatch(-$data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Rejected
                else if (Status::Approved->equals($old_status) && Status::Rejected->equals($new_status)) {
                    $this->updateClientDebit($advance->from_user_id, -$data["amount"]);
                    $wallet = $this->updateUserWallet($advance->from_user_id, -$data["amount"]);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->from_user_id);
                    NewRevenueAddedEvent::dispatch(-$data["amount"]);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                } //From Approved ==> Approved
                else if (Status::Approved->equals($old_status) && Status::Approved->equals($new_status)) {
                    $this->updateClientDebit($advance->from_user_id, $amount_difference);
                    $wallet = $this->updateUserWallet($advance->from_user_id, $amount_difference);
                    $this->createNotificationToUserAfterAdvanceChange($advance, $advance->from_user_id);
                    NewRevenueAddedEvent::dispatch($amount_difference);

                    $advance->current_balance = $wallet->amount;
                    $advance->save();
                }
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function createNoificationForAdminAfterClientAddAdvance($advance)
    {
        $advance = $advance->load("fromUser");
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $params = [
            "title" => __("The Client") . $advance->fromUser->name .  __("Has Added An Advance"),
            "content" =>  __("The Client") . $advance->fromUser->name .  __("Has Added An Advance of total ") . $advance->amount,
            "to" => $to,
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.edit",
            "routeParams" => $advance->id,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNoificationForAdminAfterClientUpdateAdvance($advance)
    {
        $advance = $advance->load("fromUser");
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $params = [
            "title" => __("The Client") . $advance->fromUser->name .  __("Has Updated Advance"),
            "content" =>  __("The Client") . $advance->fromUser->name .  __("Has Updated Advance of total ") . $advance->amount,
            "to" => $to,
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.edit",
            "routeParams" => $advance->id,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToWorkerAfterAdvancePaid($advance)
    {
        $params = [
            "title" => __("The Admin Has Gaven an Advance To you"),
            "content" => __("The Admin Has Gaven an Advance To you of total") . " " . $advance->amount,
            "to" => [
                $advance->to_user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToClientAfterAdvancePaid($advance)
    {
        $params = [
            "title" => __("The Admin Has Added an Advance From you"),
            "content" => __("The Admin Has Added an Advance From you of total") . " " . $advance->amount,
            "to" => [
                $advance->from_user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToUserAfterAdvanceChange($advance, $user_id)
    {
        $params = [
            "title" => __("The Admin Has Made Changes To Your Advance"),
            "content" => __("The Admin Has Made Changes To Your Advance of total") . " " . $advance->amount . " " . __("Its Status is ") . __($advance->status->label()),
            "to" => [
                $user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }

    public function updateClientDebit($user_id, $amount)
    {
        $client_debit = ClientDebit::where("user_id", $user_id)->first();
        $revenue_amount = $amount;
        if (isset($client_debit)) {
            if ($amount > $client_debit->amount && $client_debit->amount > 0) {
                $revenue_amount = $client_debit->amount;
            }
            ClientDebit::where("user_id", $user_id)->decrement("amount", $amount);
        } else {
            ClientDebit::create([
                "user_id" => $user_id,
                "amount" => -$amount
            ]);
        }
        return $revenue_amount;
    }
    public function updateWorkerDue($user_id, $amount)
    {
        $worker_due = WorkerDue::where("user_id", $user_id)->first();
        $expense_amount = $amount;
        if (isset($worker_due)) {
            if ($amount > $worker_due->amount && $worker_due->amount > 0) {
                $expense_amount = $worker_due->amount;
            }
            WorkerDue::where("user_id", $user_id)->decrement("amount", $amount);
        } else {
            WorkerDue::create([
                "user_id" => $user_id,
                "amount" => -$amount
            ]);
        }
        return $expense_amount;
    }

    public function updateUserWallet($user_id, $amount, $increase = true)
    {
        if (!$increase) {
            $amount = -$amount;
        }
        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet = Wallet::create([
                "user_id" => $user_id,
                "amount" => $amount
            ]);
        } else {
            $wallet->amount += $amount;
            $wallet->save();
        }
        return $wallet;
    }
}
