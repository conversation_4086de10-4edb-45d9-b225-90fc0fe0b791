<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class AttachmentInput extends Component
{
    public $label;
    public $name;
    public $id;
    public $multiple;

    public function __construct($label = 'Attachments', $name = 'attachments', $id = 'formFile', $multiple = true)
    {
        $this->label = $label;
        $this->name = $name;
        $this->id = $id;
        $this->multiple = $multiple;
    }

    public function render()
    {
        return view('components.attachment-input');
    }
}
