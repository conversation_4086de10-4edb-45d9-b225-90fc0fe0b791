<?php

namespace App\Models\Traits;

use App\Models\AuditLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

trait Auditable
{
    public static function bootAuditable()
    {
        static::created(function ($model) {
            $model->storeAuditLog('created');
        });

        static::updated(function ($model) {
            $model->storeAuditLog('updated');
        });

        static::deleted(function ($model) {
            $model->storeAuditLog('deleted');
        });
    }

    protected function storeAuditLog($action)
    {
        AuditLog::create([
            'user_id'    => Auth::id(),
            'model_type' => class_basename($this),
            'model_id'   => $this->id,
            'action'     => $action,
            'ip_address' => Request::ip(),
            'route_name' => $this->route_name
        ]);
    }
}
