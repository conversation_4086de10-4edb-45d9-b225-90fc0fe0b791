<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Expense;
use App\Models\ExpenseType;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Services\ExpenseService;
use App\Events\NewExpenseAddedEvent;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Expense\CreateRequest;
use App\Http\Requests\Admin\Expense\UpdateRequest;

class ExpenseController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new ExpenseService();
        $this->middleware('is_able:Create Expense')->only(['create', 'store']);
        $this->middleware('is_able:View Expenses')->only(['index', 'view']);
        $this->middleware('is_able:Update Expense')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Expense')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Expenses");
        return view("admin.expenses.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Expense::with("expenseType")->when($search, function ($query) use ($search) {
                $query
                    ->where("name", "LIKE", "%" . $search . "%");
            });
            if ($orderBy == "expense_type") {
                $query = $query
                    ->leftJoin('expense_types AS p', 'p.id', '=', 'expenses.expense_type_id')
                    ->orderBy('p.name', $direction)->select("expenses.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.expenses.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Expenses");
        $data["sub_title"] = __("Add New Expense");
        $data["expense_types"] = ExpenseType::where("status", 1)->get();
        return view('admin.expenses.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = Expense::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("Expenses");
        $data["sub_title"] = __("Update Expense");
        $data["expense_types"] = ExpenseType::where("status", 1)->get();
        $data["expense"] = Expense::with("attachments")->findOrFail($id);
        return view('admin.expenses.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $expense = Expense::findOrFail($id);
            Expense::destroy($id);
            NewExpenseAddedEvent::dispatch(-$expense->amount);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
    public function exportAllToPdf()
    {
        $logo = "";
        $expenses = Expense::query()->with("expenseType")->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.expenses.export_pdf', [
            'expenses' => $expenses,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_expenses.pdf", 'D');
    }
}
