<?php

namespace App\Models;

use Carbon\Carbon;
use App\Constants\UserType;
use App\Models\Traits\Auditable;
use Illuminate\Database\Eloquent\Model;
use App\Constants\ShippingInvoiceStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class ShippingInvoice extends Model
{
    use Auditable;
    protected $fillable = [
        "invoice_number",
        "user_id",
        "custom_shipping_id",
        "status",
        "notes",
        "approved_at",
        "current_balance",
        "date",
        "created_by",
        "updated_by"
    ];

    protected $casts = [
        "status" => ShippingInvoiceStatus::class
    ];

    protected $appends = [
        "created_at_tz",
        "updated_at_tz"
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function customShipping(): BelongsTo
    {
        return $this->belongsTo(CustomShipping::class);
    }
    public function shippingExpenses(): HasMany
    {
        return $this->hasMany(ShippingExpense::class);
    }
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, "attachmentable");
    }
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function totalAmount(): Attribute
    {
        $sum = $this->shippingExpenses->sum("amount");
        return Attribute::make(get: fn() => $sum);
    }
    public function totalRevenue(): Attribute
    {
        $total_revenue = 0;
        $broker_invoice = ShippingInvoice::whereHas("user", function ($query) {
            $query->where("type", UserType::Broker->value);
        })->where('custom_shipping_id', $this->custom_shipping_id)->first();
        if ($broker_invoice) {
            $broker_invoice_expenses = ShippingExpense::where("shipping_invoice_id", $broker_invoice->id)->sum("amount");
            $total_revenue = $this->total_amount - $broker_invoice_expenses;
        }

        return Attribute::make(get: fn() => $total_revenue);
    }
    public function createdAtTz(): Attribute
    {
        $created_at = $this->attributes["created_at"];
        $dateInParis = Carbon::createFromFormat('Y-m-d H:i:s', $created_at, 'UTC')
            ->setTimezone('Africa/Cairo');
        return Attribute::make(fn() => $dateInParis->toDateTimeString());
    }

    public function updatedAtTz(): Attribute
    {
        $updated_at = $this->attributes["updated_at"];
        $dateInParis = Carbon::createFromFormat('Y-m-d H:i:s', $updated_at, 'UTC')
            ->setTimezone('Africa/Cairo');
        return Attribute::make(fn() => $dateInParis->toDateTimeString());
    }
    public function routeName(): Attribute
    {
        return Attribute::make(get: fn() => "admin.custom_shipping_invoices.edit");
    }
}
