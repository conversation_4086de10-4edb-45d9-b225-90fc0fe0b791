<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\Advance;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Constants\Status;
use App\Constants\UserType;
use App\Services\AdvanceService;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Advance\CreateRequest;
use App\Http\Requests\Admin\Advance\UpdateRequest;
use App\Models\Client;

class AdvanceController extends Controller
{
    protected $advanceService;

    public function __construct()
    {
        $this->advanceService = new AdvanceService();
        $this->middleware('is_able:Create Advance')->only(['create', 'store']);
        // $this->middleware('is_able:View_Advances')->only(['index', 'view']);
        $this->middleware('is_able:Update Advance')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Advance')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Advances");
        $data["sub_title"] = __("Advances List");
        $data["users"] = User::where('id', '!=', auth("web")->id())
            ->where("IsSuperAdmin", "!=", 1)->get();
        return view('admin.advances.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $user = User::find(auth("web")->id());

            $query = Advance::with("toUser", "fromUser")
                ->when($user, function ($query) use ($user) {
                    if ($user->type == UserType::Broker && $user->can('View Related Advances')) {
                        $query->where("to_user_id", $user->id);
                    } else if ($user->type == UserType::Client && $user->can('View Related Advances')) {
                        $query->where("from_user_id", $user->id);
                    } else if ($user->type == UserType::SubClient && $user->can('View Related Advances')) {
                        $client_id =  get_client_id();
                        $client = Client::findOrFail($client_id);
                        $query->where("from_user_id", $client->user_id);
                    }
                })
                ->when($search, function ($query) use ($search) {
                    $query->whereHas("toUser", function ($q) use ($search) {
                        $q->where("name", "LIKE", "%" . $search . "%")
                            ->orWhere("email", "LIKE", "%" . $search . "%");
                    })->orWhereHas("fromUser", function ($q) use ($search) {
                        $q->where("name", "LIKE", "%" . $search . "%")
                            ->orWhere("email", "LIKE", "%" . $search . "%");
                    });
                });
            $TotalCount = $query->count();

            if ($orderBy == "to_user_name") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'advances.to_user_id')
                    ->orderBy('u.name', $direction)->select("advances.*");
            } else if ($orderBy == "from_user_name") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'advances.from_user_id')
                    ->orderBy('u.name', $direction)->select("advances.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $users = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.advances.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    public function create()
    {
        try {
            $type = request()->query("type");
            $data["title"] = __("Advances");
            if ($type == UserType::Broker->value) {
                $data["sub_title"] = __("Add Paid Advance");
            } elseif ($type == UserType::Client->value) {
                $data["sub_title"] = __("Add Received Advance");
            } else {
                $data["sub_title"] = __("Add New Advance");
            }
            $data["users"] = User::where('id', '!=', auth("web")->id())
                ->where("IsSuperAdmin", "!=", 1)->get();
            $data["type"] = $type;

            return view("admin.advances.create", $data);
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->advanceService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            // return $e;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(string $id)
    {
        try {
            $advance = Advance::findOrFail($id);
            return response()->json(['success' => true, 'data' => $advance]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $advance = Advance::findOrFail($id);
            if (auth("web")->user()->type == UserType::Client && $advance->from_user_id != auth("web")->id()) {
                abort(401);
            }

            $data["title"] = __("Advances");
            $data["sub_title"] = __("Update Advance");
            $data["users"] = User::where('id', '!=', auth("web")->id())
                ->where("IsSuperAdmin", "!=", 1)->get();
            $advance = $advance->load("attachments");
            $data["advance"] = $advance;
            $data["statuses"] = Status::availableTypes();

            return view("admin.advances.edit", $data);
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->advanceService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $this->advanceService->destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = "";
        $user = User::find(auth("web")->id());
        $advances = Advance::with("toUser", "fromUser")
            ->when($user, function ($query) use ($user) {
                if ($user->type == UserType::Broker && $user->can('View Related Advances')) {
                    $query->where("to_user_id", $user->id);
                } else if ($user->type == UserType::Client && $user->can('View Related Advances')) {
                    $query->where("from_user_id", $user->id);
                } else if ($user->type == UserType::SubClient && $user->can('View Related Advances')) {
                    $client_id =  get_client_id();
                    $client = Client::findOrFail($client_id);
                    $query->where("from_user_id", $client->user_id);
                }
            })
            ->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.advances.export_pdf', [
            'advances' => $advances,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_advances.pdf", 'D');
    }
}
