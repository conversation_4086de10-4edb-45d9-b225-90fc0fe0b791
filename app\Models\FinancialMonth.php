<?php

namespace App\Models;

use App\Constants\FinancialMonthStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinancialMonth extends Model
{
    protected $fillable = [
        "name",
        "start_from",
        "end_to",
        "status",
        "financial_year_id",
        "start_balance",
        "end_balance",
        "total_expenses",
        "total_revenues",
        "withdrawn_revenues",
        "created_by",
        "updated_by"
    ];

    protected $casts = [
        "status" => FinancialMonthStatus::class
    ];
    public function financialYear(): BelongsTo
    {
        return $this->belongsTo(FinancialYear::class);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }
}
