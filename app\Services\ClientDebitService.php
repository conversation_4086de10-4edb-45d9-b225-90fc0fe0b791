<?php

namespace App\Services;

use Exception;
use App\Models\User;
use App\Models\Advance;
use App\Constants\Status;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Support\Str;
use App\Constants\WorkerDueAction;
use Illuminate\Support\Facades\DB;
use App\Events\NewRevenueAddedEvent;
use App\Constants\AttachmentVisibleStatus;

class ClientDebitService
{
    public function updateClientDebit(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $client_debit = ClientDebit::findOrFail($id);
            if (auth("web")->user()->IsSuperAdmin) {
                if ($data["action_type"] == WorkerDueAction::Update->value) {
                    $client_debit->amount = $data["amount"];
                } else if ($data["action_type"] == WorkerDueAction::PartialPay->value) {
                    $client_debit->amount -= $data["amount"];

                    $data["status"] = Status::Approved->value;
                    $this->createAdvance($data);
                } else if ($data["action_type"] == WorkerDueAction::FullPay->value) {
                    $data["status"] = Status::Approved->value;
                    $data["amount"] = $client_debit->amount;
                    $this->createAdvance($data);
                    $client_debit->amount = 0;
                }
                $client_debit->save();
            } else if (auth("web")->user()->type == UserType::Client) {
                if ($data["action_type"] == WorkerDueAction::PartialPay->value) {
                    $data["status"] = Status::Pending->value;
                    $data["from_user_id"] = $client_debit->user_id;
                    $this->createAdvance($data);
                } else if ($data["action_type"] == WorkerDueAction::FullPay->value) {
                    $data["status"] = Status::Pending->value;
                    $data["amount"] = $client_debit->amount;
                    $data["from_user_id"] = $client_debit->user_id;
                    $this->createAdvance($data);
                }
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    private function createAdvance($data)
    {
        $advance = Advance::create([
            "to_user_id" => @$data["to_user_id"],
            "from_user_id" => @$data["from_user_id"],
            "amount" => $data["amount"],
            "date" => @$data["date"],
            "notes" => @$data["notes"],
            "status" => @$data["status"],
            "created_by" => auth("web")->id()
        ]);

        if (isset($data["attachments"])) {
            foreach ($data["attachments"] as $attachment) {
                $original_fielname = $attachment->getClientOriginalName();
                $mime_type = $attachment->getClientOriginalExtension();
                $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                $path         = "uploads/advances";
                $uploadPath   = public_path($path);
                $attachment->move($uploadPath, $uniqueName);
                $file_path = $path . "/" . $uniqueName;

                $advance->attachments()->create([
                    "file_path" => $file_path,
                    "filename" => $uniqueName,
                    "original_filename" => $original_fielname,
                    "mime_type" => $mime_type,
                    "visible_to" => AttachmentVisibleStatus::Client
                ]);
            }
        }
        NewRevenueAddedEvent::dispatch($advance->amount);
        if (is_client() && !is_admin()) {
            $this->createNoificationForAdminAfterClientAddAdvance($advance);
        }
    }
    public function createNoificationForAdminAfterClientAddAdvance($advance)
    {
        $advance = $advance->load("fromUser");
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $params = [
            "title" => __("The Client") . $advance->fromUser->name .  __("Has Added An Advance"),
            "content" =>  __("The Client") . $advance->fromUser->name .  __("Has Added An Advance of total ") . $advance->amount,
            "to" => $to,
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.edit",
            "routeParams" => $advance->id,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
}
