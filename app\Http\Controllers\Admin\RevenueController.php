<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Revenue;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\FinanceCategory;
use App\Services\RevenueService;
use Illuminate\Support\Facades\DB;
use App\Events\NewExpenseAddedEvent;
use App\Events\NewRevenueAddedEvent;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Revenue\CreateRequest;
use App\Http\Requests\Admin\Revenue\UpdateRequest;
use App\Models\Client;

class RevenueController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new RevenueService();
        $this->middleware('is_able:Create Revenue')->only(['create', 'store']);
        $this->middleware('is_able:View Revenues')->only(['index', 'view']);
        $this->middleware('is_able:Update Revenue')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Revenue')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Revenues");
        $categories = DB::table("finance_categories AS c")
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_purchases FROM purchases GROUP BY finance_category_id) AS p"), function ($join) {
                $join->on("p.finance_category_id", "=", "c.id");
            })
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_revenues FROM revenues GROUP BY finance_category_id) AS r"), function ($join) {
                $join->on("r.finance_category_id", "=", "c.id");
            })
            ->select(
                "c.id",
                "c.name",
                DB::raw('COALESCE(p.total_purchases, 0) as total_purchases'),
                DB::raw('COALESCE(r.total_revenues, 0) as total_revenues')
            )
            ->get();
        $financeCategories = FinanceCategory::all();
        $current_type = request()->query("category");

        $data["categories"] = $categories;
        $data["financeCategories"] = $financeCategories;
        $data["current_type"] = $current_type;
        return view("admin.revenues.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";
            $selectedType = $request->SelectedType;
            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Revenue::with("financeCategory", "attachments", "client")
                ->when($selectedType && $selectedType != "all", function ($query) use ($selectedType) {
                    $query->where("finance_category_id", $selectedType);
                })
                ->when($search, function ($query) use ($search) {
                    $query->whereHas("financeCategory", function ($q) use ($search) {
                        $q->where("name", "LIKE", "%" . $search . "%");
                    });
                });
            if ($orderBy == "finance_category") {
                $query = $query
                    ->leftJoin('finance_categories AS f', 'f.id', '=', 'revenues.finance_category_id')
                    ->orderBy('f.name', $direction)->select("revenues.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.revenues.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Revenues");
        $data["sub_title"] = __("Add New Revenue");
        $data["clients"] = Client::query()->get(['id', 'name_ar', "name_en"]);
        $data["finance_categories"] = FinanceCategory::where("status", 1)->get();
        return view('admin.revenues.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = Revenue::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("revenues");
        $data["sub_title"] = __("Update Revenue");
        $data["finance_categories"] = FinanceCategory::where("status", 1)->get();
        $data["revenue"] = Revenue::with("attachments")->findOrFail($id);
        $data["clients"] = Client::query()->get(['id', 'name_ar', "name_en"]);
        return view('admin.revenues.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $revenue = Revenue::findOrFail($id);
            Revenue::destroy($id);
            NewRevenueAddedEvent::dispatch(-$revenue->amount);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
    public function exportAllToPdf()
    {
        $logo = "";
        $current_type = request()->query("category");
        $revenues = Revenue::query()->with("financeCategory")
            ->when($current_type && $current_type != "all", function ($query) use ($current_type) {
                $query->where("finance_category_id", $current_type);
            })
            ->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.revenues.export_pdf', [
            'revenues' => $revenues,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_revenues.pdf", 'D');
    }
}
