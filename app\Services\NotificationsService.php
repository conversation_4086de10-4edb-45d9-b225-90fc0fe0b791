<?php

namespace App\Services;

use App\Models\Notification;
use Exception;

class NotificationsService
{
    /**
     * create notification for user
     * @param array $params
     * @return void
     */
    public function createNotification($params)
    {
        try {
            if (@$params["to"] && count($params["to"]) > 0) {
                foreach ($params["to"] as $id) {
                    $notification = Notification::create([
                        "title" => $params["title"],
                        "content" => $params["content"],
                        "user_id" => $id,
                        "route" => $params["route"],
                        "from_user_id" => auth("web")->id(),
                        "routeParams" => json_encode($params["routeParams"]),
                        "reference_id" => $params["reference_id"],
                        "reference_type" => $params["reference_type"],
                    ]);
                }
            } else {
                $notification = Notification::create([
                    "title" => $params["title"],
                    "content" => $params["content"],
                    "user_id" => $params["user_id"],
                    "route" => $params["route"],
                    "from_user_id" => auth("web")->id(),
                    "routeParams" => json_encode($params["routeParams"]),
                    "reference_id" => $params["reference_id"],
                    "reference_type" => $params["reference_type"],
                ]);
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
