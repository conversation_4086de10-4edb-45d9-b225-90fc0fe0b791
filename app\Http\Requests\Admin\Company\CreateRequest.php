<?php

namespace App\Http\Requests\Admin\Company;

use App\Constants\CrudMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "name_ar" => "required",
            "name_en" => "required",
            "client_id" => "required|integer",
            "notes" => "nullable",
        ];
    }


    public function messages(): array
    {
        return [
            "name_ar.required" => CrudMessage::isRequired(__("Arabic Name")),
            "name_en.required" => CrudMessage::isRequired(__("English Name")),
            "client_id.required" => CrudMessage::isRequired(__("Client")),
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'created_by' => auth("web")->id(),
        ]);
    }
}
