<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\WithdrawRevenue;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Constants\FinancialMonthStatus;
use App\Services\WithdrawRevenueService;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\WithdrawRevenue\CreateRequest;
use App\Http\Requests\Admin\WithdrawRevenue\UpdateRequest;
use App\Models\FinancialMonth;

class WithdrawRevenueController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new WithdrawRevenueService();
        $this->middleware('is_able:Create Withdraw Revenue')->only(['create', 'store']);
        $this->middleware('is_able:View Withdraw Revenues')->only(['index', 'view']);
        $this->middleware('is_able:Update Withdraw Revenue')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Withdraw Revenue')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Withdraw Revenue");
        return view("admin.withdraw_revenues.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = WithdrawRevenue::with("financialMonth")->when($search, function ($query) use ($search) {
                $query
                    ->where("date", "LIKE", "%" . $search . "%");
            });

            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.withdraw_revenues.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Withdraw Revenue");
        $data["sub_title"] = __("Withdraw Revenue");
        $data["financial_month"] = FinancialMonth::where("status", FinancialMonthStatus::Opened)->first();
        return view('admin.withdraw_revenues.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = WithdrawRevenue::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("Withdraw Revenue");
        $data["sub_title"] = __("Update Withdraw Revenue");
        $data["financial_month"] = FinancialMonth::where("status", FinancialMonthStatus::Opened)->get();
        $data["withdraw"] = WithdrawRevenue::with("attachments")->findOrFail($id);
        return view('admin.withdraw_revenues.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $withdraw = WithdrawRevenue::findOrFail($id);
            $current_month = FinancialMonth::where('status', FinancialMonthStatus::Opened)->first();
            $current_month->withdrawn_revenues -= $withdraw->amount;
            $current_month->save();

            WithdrawRevenue::destroy($id);

            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
    public function exportAllToPdf()
    {
        $logo = "";
        $withdraws = WithdrawRevenue::query()->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.withdraw_revenues.export_pdf', [
            'withdraws' => $withdraws,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_withdraws.pdf", 'D');
    }
}
