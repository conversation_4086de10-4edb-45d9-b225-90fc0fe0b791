<?php

namespace App\Http\Requests\Admin\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "current_password" => "required",
            "new_password" => "required|min:6|max:20",
            "password_confirmation" => "required",
        ];
    }


    public function messages(): array
    {
        return [
            "current_password.required" => __("this field is required"),
            "new_password.required" => __("this field is required"),
            "new_password.confirmed" => __("Password does not match"),
            "new_password.min" => __("not allowed less than 6 characters"),
            "new_password.max" => __("not allowed more than 20 characters"),
            "password_confirmation.required" => __("this field is required"),
        ];
    }
}
