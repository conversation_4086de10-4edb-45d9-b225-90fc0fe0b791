<?php

namespace App\Constants;

enum CustomShippingType: int
{
    case Internal = 1;
    case External = 2;
    case Imported = 3;
    case Exported = 4;


    public static function availableTypes()
    {
        return [
            "Internal" => self::Internal,
            "External" => self::External
        ];
    }
    public static function availableTypes2()
    {
        return [
            "Imported" => self::Imported,
            "Exported" => self::Exported
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::Internal => "Internal",
            self::External => "External",
            self::Imported => "Imported",
            self::Exported => "Exported",
        };
    }
}
