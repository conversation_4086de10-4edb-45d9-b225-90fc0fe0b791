<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $title
 * @property string $content
 * @property int $user_id
 * @property string $route
 * @property string $routeParams
 * @property mixed $read_at
 * @property int $reference_id
 * @property string $reference_type
 * @property User $users
 */
class Notification extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        "title",
        "content",
        "user_id",
        "from_user_id",
        "route",
        "routeParams",
        "read_at",
        "reference_id",
        "reference_type",
        "created_by",
        "updated_by",
        "deleted_by"
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function fromUser()
    {
        return $this->belongsTo(User::class, "from_user_id");
    }
}
