<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('short_name', 5)->nullable();
            $table->string('name', 100)->nullable();
            $table->string('name_ar', 100)->nullable();
            $table->string('name_en', 100)->nullable();
            $table->string('iso3', 100)->nullable();
            $table->string('number_code', 100)->nullable();
            $table->string('phone_code', 100)->nullable();
            $table->boolean('is_default')->default(false);
            $table->integer("type")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
