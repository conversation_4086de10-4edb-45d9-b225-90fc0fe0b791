<?php

namespace App\Services;

use Mpdf\Mpdf;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;

class PdfService
{
    protected Mpdf $pdf;

    public function __construct(array $options = [])
    {
        // Get default font directories
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        // Get default font data
        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];


        $defaultOptions = [
            'mode' => 'utf-8',
            'format' => 'A4',
            'orientation' => 'portrait',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'fontDir' => array_merge($fontDirs, [
                public_path('fonts'),
            ]),
            'fontdata' => $fontData,
            'default_font' => 'Almarai',
            'debug' => false,
            'shrink_tables_to_fit' => 1,
            'use_kwt' => true,
            'keepColumns' => true,
            'table_error_report' => false,
            'simpleTables' => true,
            'autoScriptToLang' => true,
            'autoLangToFont' => true,
            'allow_charset_conversion' => false,
            'setAutoTopMargin' => 'stretch',
            'setAutoBottomMargin' => 'stretch',
            'useSubstitutions' => true,
            'compress' => true,
        ];

        // Merge custom options with defaults
        $mergedOptions = array_merge($defaultOptions, $options);

        $this->pdf = new Mpdf($mergedOptions);

        $this->configureSettings();
    }

    protected function configureSettings(): void
    {

        $this->pdf->SetHTMLFooter('<div style="text-align: center; font-size: 9pt; color: #333; border-top: 1px solid #ccc; padding-top: 3mm;">
            ' . __('Generated by') . ' ' . config('app.name') . '
        </div>');

        // Set display preferences
        $this->pdf->SetDisplayMode('fullpage');

        // Set document properties
        $this->pdf->SetTitle(config('app.name') . ' - ' . __('Data Export'));
        $this->pdf->SetCreator(config('app.name'));

        // Set better text handling
        $this->pdf->adjustFontDescLineheight = 1.2;
        $this->pdf->falseBoldWeight = 10;

        // Add CSS for better styling
        $this->pdf->WriteHTML('
        <style>
            body { color: #333; line-height: 1.5; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 1rem; }
            table th { background-color: #f8f9fa; color: #333; font-weight: bold; text-align: right; }
            table td, table th { border: 1px solid #dee2e6; padding: 8px; }
            table tr:nth-child(even) { background-color: #f2f2f2; }
            h1, h2, h3 { color: #3366cc; }
            .header { background-color: #f3f4f6; padding: 10px; margin-bottom: 20px; }
            .footer { text-align: center; font-size: 9pt; color: #777; border-top: 1px solid #ccc; padding-top: 3mm; }
        </style>
        ');
    }

    public function generatePdf(string $view, array $data = []): Mpdf
    {
        $html = view($view, $data)->render();
        $this->pdf->WriteHTML($html);

        return $this->pdf;
    }

    public function download(string $filename): void
    {
        $this->pdf->Output($filename, 'D');
    }

    public function save(string $filepath): void
    {
        $this->pdf->Output($filepath, 'F');
    }

    public function stream(string $filename): void
    {
        $this->pdf->Output($filename, 'I');
    }
}
