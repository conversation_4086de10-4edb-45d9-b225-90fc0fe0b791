<?php

use App\Models\Client;
use App\Models\Setting;
use App\Constants\UserType;
use App\Models\Notification;
use App\Models\FinancialYear;
use App\Models\FinancialMonth;
use App\Models\ShippingInvoice;
use Illuminate\Support\Facades\DB;
use App\Constants\FinancialMonthStatus;


if (!function_exists("generateRandomNumberString")) {
    function generateRandomNumberString($length = 8)
    {
        $digits = '0123456789';
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $index = mt_rand(0, strlen($digits) - 1);
            $randomString .= $digits[$index];
        }

        return $randomString;
    }
}

if (!function_exists("generateUniqueRandomNumberStringToTable")) {
    function generateUniqueRandomNumberStringToTable($table, $col_name, $length = 8)
    {
        do {
            $number = generateRandomNumberString($length);
            $extists = DB::table($table)->where($col_name, $number)->exists();
        } while ($extists);

        return $number;
    }
}
if (!function_exists("generateInvoiceNumber")) {
    function generateInvoiceNumber()
    {
        $invoice = ShippingInvoice::query()->orderBy("invoice_number", "desc")->first();
        return $invoice != null ? $invoice->invoice_number + 1 : 1;
    }
}
if (!function_exists("generateClientInvoiceNumber")) {
    function generateClientInvoiceNumber()
    {
        $invoice = ShippingInvoice::query()->whereHas("user", fn($q) => $q->where("type", UserType::Client->value))->orderBy("invoice_number", "desc")->first();
        return $invoice != null ? $invoice->invoice_number + 1 : 1;
    }
}
if (!function_exists("getClientNextInvoiceNumber")) {
    function getClientNextInvoiceNumber($user_id)
    {
        $invoice = ShippingInvoice::query()->where("user_id", $user_id)->orderBy("invoice_number", "desc")->first();
        return $invoice != null ? $invoice->invoice_number + 1 : 1;
    }
}


if (!function_exists("unread_notifications")) {
    function unread_notifications()
    {
        return Notification::where(['user_id' => auth("web")->id(), 'read_at' => NULL])
            ->with("fromUser.avatar")
            ->latest('id')
            ->take(6)->get();
    }
}
if (!function_exists("get_site_name")) {
    function get_site_name()
    {
        return Setting::where("key", "site_name")->first()?->value;
    }
}
if (!function_exists("get_setting")) {
    function get_setting($key)
    {
        return Setting::where("key", $key)->first()?->value;
    }
}
if (!function_exists("is_admin")) {
    function is_admin()
    {
        return auth("web")->user()->IsSuperAdmin;
    }
}
if (!function_exists("is_employee")) {
    function is_employee()
    {
        return !auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::Employee;
    }
}
if (!function_exists("is_broker")) {
    function is_broker()
    {
        return !auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::Broker;
    }
}
if (!function_exists("is_system_employee")) {
    function is_system_employee()
    {
        return !auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::SystemEmployee;
    }
}
if (!function_exists("is_client")) {
    function is_client()
    {
        return !auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::Client;
    }
}
if (!function_exists("is_sub_client")) {
    function is_sub_client()
    {
        return !auth("web")->user()->IsSuperAdmin && auth("web")->user()->type == UserType::SubClient;
    }
}
if (!function_exists("get_client_id")) {
    function get_client_id()
    {
        $client_id = null;
        if (is_client()) {
            $client = Client::where("user_id", auth("web")->id())->first(['id']);
            $client_id = $client->id;
        } else if (is_sub_client()) {
            $client_id = auth("web")->user()->client_id;
        }
        return $client_id;
    }
}
if (!function_exists("get_client")) {
    function get_client()
    {
        $client = null;
        if (is_client()) {
            $client = Client::where("user_id", auth("web")->id())->first();
        } else if (is_sub_client()) {
            $client = Client::find(auth("web")->user()->client_id);
        }
        return $client;
    }
}

if (!function_exists("get_invoice_expenses")) {
    function get_invoice_expenses($id)
    {
        $invoice = ShippingInvoice::with("shippingExpenses")->findOrFail($id);
        return $invoice->shippingExpenses->sum("amount");
    }
}

if (!function_exists("get_current_month")) {
    function get_current_month()
    {
        $year = FinancialYear::where("status", 1)->first();
        $month = FinancialMonth::where("financial_year_id", $year->id)->where("status", FinancialMonthStatus::Opened)->first();
        return $month;
    }
}
if (!function_exists("get_current_year")) {
    function get_current_year()
    {
        $year = FinancialYear::where("status", 1)->first();
        return $year;
    }
}
