<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use App\Constants\CustomShippingStatus as CustomShippingStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CustomShipping\UpdateCustomShippingStatusRequest;
use App\Models\CustomShipping;
use App\Services\CustomShippingService;
use Exception;
use Illuminate\Http\Request;

class CustomShippingStatusController extends Controller
{
    public function __construct(protected CustomShippingService $shippingStatusService) {}
    public function update(UpdateCustomShippingStatusRequest $request, $id)
    {
        // return $request->all();
        try {
            $this->shippingStatusService->updateStatus($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            // return $ex;
            session()->flash("error", $ex->getMessage());
            return back();
        }
    }
}
