<?php

namespace App\Services;

use Exception;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WorkerDue;
use App\Models\Attachment;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Models\CustomShipping;
use App\Models\ShippingExpense;
use App\Models\ShippingInvoice;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\ShippingInvoiceStatus;
use App\Constants\AttachmentVisibleStatus;

class ShippingInvoiceService
{

    public function storeClientInvoice(array $data)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::create($data);
            $this->handleAttachments($data, $invoice);
            $this->handleInvoiceSubClient($data, $invoice);
            $this->handleApprovedAtDate($invoice, $data["status"]);
            $this->handleClientAttachmentsVisiblityOnCreate($data, $invoice);
            $total_amount = $this->handleShippingExpenses($data, $invoice);

            $current_balance = $this->updateClientDebitWithWallet($total_amount, $invoice->user_id);
            $invoice->current_balance = $current_balance;
            $invoice->save();
            $this->createNotificationToClientAfterApproval($invoice);
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }
    /**
     *  update a shipping invoice
     * create or update shipping expenses
     * upload attachments
     * change status of invoice and add balance to worker
     * @param array $data
     * @return void
     */
    public function createOrUpdateInvoice(array $data)
    {
        try {
            $status_changed = false;
            DB::beginTransaction();
            $invoice = ShippingInvoice::with("user", "shippingExpenses")->findOrFail($data["invoice_id"]);

            $old_status = $invoice->status->value;
            $new_status =  $data["status"];
            $invoice->updated_by = auth("web")->id();
            $invoice->notes =  @$data["notes"];

            if (isset($data["status"]) && $invoice->status->value != $data["status"]) {
                $status_changed = true;
                $invoice->status = $data["status"];
                $invoice->save();
            }

            $invoice->save();
            $this->handleAttachments($data, $invoice);
            $this->handleInvoiceSubClient($data, $invoice);
            $this->handleApprovedAtDate($invoice, $new_status);
            $this->handleClientAttachmentsVisiblityOnUpdate($data);
            $total_amount_before = $invoice->total_amount;
            $total_amount = $this->handleShippingExpenses($data, $invoice);

            $current_balance = null; //Wallet::where('user_id', $invoice->user_id)->first()?->amount ?? 0
            //From Pending/Rejected ==> Approved
            if ($status_changed && $data["status"] == ShippingInvoiceStatus::Approved->value) {
                Log::channel("clearo")->debug("First Case");

                if ($invoice->user->type == UserType::Broker) {
                    $current_balance =  $this->updateWorkerDueWithWallet($total_amount, $invoice->user_id);
                    $this->createNotificationToWorkerAfterApproval($invoice);
                } elseif ($invoice->user->type == UserType::Client) {
                    $current_balance = $this->updateClientDebitWithWallet($total_amount, $invoice->user_id);
                    $this->createNotificationToClientAfterApproval($invoice);
                }
                if ($current_balance != null) {
                    Log::channel("clearo")->debug("First Case", ["balance" => $current_balance]);
                    $invoice->current_balance = $current_balance;
                    $invoice->save();
                }
            }
            //From Approved ==> Pending/Rejected
            if (ShippingInvoiceStatus::Approved->equals($old_status) && (ShippingInvoiceStatus::Pending->equals($new_status) || ShippingInvoiceStatus::Rejected->equals($new_status))) {
                Log::channel("clearo")->debug("Second Case");
                if ($invoice->user->type == UserType::Broker) {
                    $current_balance = $this->updateWorkerDueWithWallet($total_amount, $invoice->user_id, false);
                } elseif ($invoice->user->type == UserType::Client) {
                    $current_balance =   $this->updateClientDebitWithWallet($total_amount, $invoice->user_id, false);
                }
                if ($current_balance != null) {
                    Log::channel("clearo")->debug("Second Case", ["balance" => $current_balance]);
                    $invoice->current_balance = $current_balance;
                    $invoice->save();
                }
            }
            //From Approved ==> Approved
            if (ShippingInvoiceStatus::Approved->equals($old_status) && ShippingInvoiceStatus::Approved->equals($new_status)) {
                Log::channel("clearo")->debug("Third Case");
                $total_amount_after = $total_amount - $total_amount_before;
                if ($invoice->user->type == UserType::Broker) {
                    $current_balance =   $this->updateWorkerDueWithWallet($total_amount_after, $invoice->user_id);
                } elseif ($invoice->user->type == UserType::Client) {
                    $current_balance = $this->updateClientDebitWithWallet($total_amount_after, $invoice->user_id);
                }
                // It Means their are changes happended to invoice expenses amount
                if ($total_amount_after != 0) {
                    Log::channel("clearo")->debug("Third Case", ["balance" => $current_balance, "total_amount_after" => $total_amount_after]);
                    $invoice->current_balance = $current_balance;
                    $invoice->save();
                }
            }
            if (is_broker() && !is_admin()) {
                $this->createNoificationForAdminAfterInvoiceChange($invoice);
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }
    public function createClientInvoice($invoice_id)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::findOrFail($invoice_id);
            $custom_shipping = CustomShipping::findOrFail($invoice->custom_shipping_id);
            $custom_shipping = $custom_shipping->load("client");
            $invoice = $invoice->load("shippingExpenses", "attachments");

            $invoice_attributes = $invoice->toArray();

            $newAttributes = Arr::except($invoice_attributes, ['id', 'created_at', 'updated_at']);
            $newAttributes["user_id"] = $custom_shipping->client->user_id;
            $newAttributes["status"] = ShippingInvoiceStatus::Pending;
            $wallet = Wallet::where("user_id", $custom_shipping->client->user_id)->first();
            $newAttributes["current_balance"] = $wallet?->amount;
            $new_invoice = ShippingInvoice::create($newAttributes);
            foreach ($invoice->shippingExpenses as $expense) {
                $new_invoice->shippingExpenses()->create([
                    "shipping_expense_type_id" => $expense->shipping_expense_type_id,
                    "amount" => $expense->amount
                ]);
            }
            foreach ($invoice->attachments as $attachment) {
                $new_invoice->attachments()->create([
                    "file_path" => $attachment->file_path,
                    "filename" => $attachment->filename,
                    "original_filename" => $attachment->original_filename,
                    "mime_type" => $attachment->mime_type,
                    "visible_to" => AttachmentVisibleStatus::Client
                ]);
            }
            DB::commit();
            return $new_invoice->id;
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }
    public function deleteInvoice($id)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::with("attachments", "shippingExpenses")->findOrFail($id);
            $total_amount = $invoice->total_amount;
            foreach ($invoice->attachments as $attachment) {
                $attachment->delete();
            }
            foreach ($invoice->shippingExpenses as $expense) {
                $expense->delete();
            }
            if ($invoice->status == ShippingInvoiceStatus::Approved) {
                $clientDebit = ClientDebit::where(['user_id' => $invoice->user_id])->first();
                if (isset($clientDebit)) {
                    $clientDebit->amount -= $total_amount;
                    $clientDebit->updated_by = auth("web")->id();
                    $clientDebit->save();
                }

                $workerDue = WorkerDue::where(['user_id' => $invoice->user_id])->first();
                if (isset($workerDue)) {
                    $workerDue->amount -= $total_amount;
                    $workerDue->updated_by = auth("web")->id();
                    $workerDue->save();
                }

                $wallet = Wallet::where(['user_id' => $invoice->user_id])->first();
                if (isset($wallet) && isset($clientDebit)) {
                    $wallet->amount +=  $total_amount;
                    $wallet->save();
                }
                if (isset($wallet) && isset($workerDue)) {
                    $wallet->amount -=  $total_amount;
                    $wallet->save();
                }
            }

            $invoice->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    private function handleAttachments(array $data, $invoice): void
    {
        if (isset($data["attachments"])) {
            foreach ($data["attachments"] as $attachment) {
                $original_fielname = $attachment->getClientOriginalName();
                $mime_type = $attachment->getClientOriginalExtension();
                $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                $path         = "uploads/custom_shippings/$invoice->custom_shipping_id/invoices";
                $uploadPath   = public_path($path);
                $attachment->move($uploadPath, $uniqueName);
                $file_path = $path . "/" . $uniqueName;

                $invoice->attachments()->create([
                    "file_path" => $file_path,
                    "filename" => $uniqueName,
                    "original_filename" => $original_fielname,
                    "mime_type" => $mime_type,
                    "visible_to" => AttachmentVisibleStatus::Employee
                ]);
            }
        }
    }

    private function handleShippingExpenses(array $data, $invoice): float
    {
        $total_amount = 0.00;
        foreach ($data["shipping_expense"] as $shipping_expense) {
            $expense_id = @$shipping_expense["id"];
            $total_amount += (float) $shipping_expense["amount"];
            if (isset($expense_id)) {
                $expense = ShippingExpense::findOrFail($expense_id);
                $expense->shipping_expense_type_id = $shipping_expense["shipping_expense_type_id"];
                $expense->amount = $shipping_expense["amount"];
                $expense->notes = $shipping_expense["notes"];
                $expense->save();
            } else {
                ShippingExpense::updateOrCreate([
                    "shipping_invoice_id" => $invoice->id,
                    "shipping_expense_type_id" => $shipping_expense["shipping_expense_type_id"],
                ], [
                    "amount" => $shipping_expense["amount"],
                    "notes" => $shipping_expense["notes"],
                ]);
            }
        }
        return $total_amount;
    }

    private function updateWorkerDueWithWallet($total_amount, $user_id, $increase = true)
    {
        if (!$increase) {
            $total_amount = -$total_amount;
        }
        $workerDue = WorkerDue::where(['user_id' => $user_id])->first();
        if (!isset($workerDue)) {
            $workerDue = WorkerDue::create([
                "user_id" => $user_id,
                "amount" => $total_amount,
                "created_by" => auth("web")->id()
            ]);
        } else {
            $workerDue->amount += $total_amount;
            $workerDue->updated_by = auth("web")->id();
            $workerDue->save();
        }

        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet = Wallet::create([
                "user_id" => $user_id,
                "amount" => $total_amount
            ]);
        } else {
            $wallet->amount += $total_amount;
            $wallet->save();
        }

        return $wallet->amount;
    }

    private function updateClientDebitWithWallet($total_amount, $user_id,  $increase = true)
    {
        if (!$increase) {
            $total_amount -= $total_amount;
        }
        $clientDebit = ClientDebit::where(['user_id' => $user_id])->first();
        if (!isset($clientDebit)) {
            $clientDebit = ClientDebit::create([
                "user_id" => $user_id,
                "amount" => $total_amount,
                "created_by" => auth("web")->id()
            ]);
        } else {
            $clientDebit->amount += $total_amount;
            $clientDebit->updated_by = auth("web")->id();
            $clientDebit->save();
        }

        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet = Wallet::create([
                "user_id" => $user_id,
                "amount" => -$total_amount
            ]);
        } else {
            $wallet->amount -= $total_amount;
            $wallet->save();
        }

        return $wallet->amount;
    }
    private function handleInvoiceSubClient(array $data, $invoice): void
    {
        if (isset($data["users"])) {
            $invoice->users()->sync($data["users"]);
        }
    }
    private function handleApprovedAtDate($shippingInvoice, $status)
    {
        if (ShippingInvoiceStatus::Approved->equals($status) && !$shippingInvoice->approved_at) {
            $shippingInvoice->approved_at = now();
        } else if (!ShippingInvoiceStatus::Approved->equals($status) && $shippingInvoice->approved_at) {
            $shippingInvoice->approved_at = null;
        }
        $shippingInvoice->save();
    }
    private function createNotificationToWorkerAfterApproval($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $params = [
            "title" => __("The Admin Has Approved The Invoice"),
            "content" => __("The Admin Has Approved The Invoice Of Code") . " " . $shippingInvoice->invoice_code,
            "to" => [
                $shippingInvoice->user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.edit",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }
    private function createNotificationToClientAfterApproval($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $params = [
            "title" => __("The Admin Has Created An Invoice For You"),
            "content" => __("The Admin Has Created An Invoice Of Code") . " " . $shippingInvoice->invoice_code . " " . __("For The Shipping Of Plicy Number") . " " . $shippingInvoice->customShipping->policy_number,
            "to" => [
                $shippingInvoice->user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.exportPdf",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }

    private function createNoificationForAdminAfterInvoiceChange($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $params = [
            "title" => __("The Broker Has Made Changes To The Invoice"),
            "content" => __("The Broker Has Made Changes To The Invoice Of Code") . " " . $shippingInvoice->invoice_code,
            "to" => $to,
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.edit",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }

    private function handleClientAttachmentsVisiblityOnCreate($data, $invoice)
    {
        try {
            if (!isset($data["attachment_users"])) return;

            foreach ($data["attachment_users"] as $id => $users_list) {
                $attachment = Attachment::findOrFail($id);
                $new_attachment =  $invoice->attachments()->create([
                    "file_path" => $attachment->file_path,
                    "filename" => $attachment->filename,
                    "original_filename" => $attachment->original_filename,
                    "mime_type" => $attachment->mime_type,
                    "visible_to" => AttachmentVisibleStatus::Client
                ]);

                $new_attachment->users()->sync($users_list);
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }
    private function handleClientAttachmentsVisiblityOnUpdate($data)
    {
        try {
            if (!isset($data["attachment_users"])) return;

            foreach ($data["attachment_users"] as $id => $users_list) {
                $attachment = Attachment::findOrFail($id);
                $attachment->users()->sync($users_list);
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
