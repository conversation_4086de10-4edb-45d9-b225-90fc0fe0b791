<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string("name")->nullable();
            $table->string('name_ar', 200)->nullable();
            $table->string('name_en', 200)->nullable();
            $table->string("phone")->nullable();
            $table->string("address")->nullable();
            $table->string("company_name")->nullable();
            $table->foreignId("user_id")->nullable()->constrained("users")->onDelete("cascade");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
