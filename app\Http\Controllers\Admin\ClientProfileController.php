<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Client;
use App\Models\Wallet;
use App\Models\Advance;
use App\Models\Setting;
use App\Models\Document;
use App\Constants\Status;
use App\Models\ClientDebit;
use App\Models\DocumentType;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\CustomShipping;
use App\Models\ShippingInvoice;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Constants\CustomShippingStatus;
use App\Constants\ShippingInvoiceStatus;
use Yajra\DataTables\Facades\DataTables;

class ClientProfileController extends Controller
{
    public function index($id)
    {
        try {
            $data["title"] = __("Clients");
            $data["sub_title"] = __("Client Profile");
            $client = Client::findOrFail($id);
            $client = $client->load("user.avatar");
            $data["client"] = $client;
            $data["total_advances"] = Advance::where("from_user_id", $client->user_id)->where("status", Status::Approved)->sum("amount");
            $data["total_debit"] = ClientDebit::where("user_id", $client->user_id)->first()->amount ?? 0;
            $data["client_debit"] = ClientDebit::where("user_id", $client->user_id)->first();

            $data["custom_shippings_count"] = CustomShipping::where("client_id", $id)->count();
            $data["active_custom_shippings_count"] = CustomShipping::where("client_id", $id)
                ->where("status", "<=", CustomShippingStatus::RELEASED)
                ->count();
            $invoices = ShippingInvoice::where("user_id", $client->user_id)->with("shippingExpenses")->get();
            $data["total_invoices_amount"] = $invoices->sum(fn($el) => $el->total_amount);

            $data["wallet_balance"] = Wallet::where("user_id", $client->user_id)->value("amount");
            return view('admin.clients.profile', $data);
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    public function loadAdvances($id)
    {
        $client = Client::findOrFail($id);
        $advances = Advance::query()->where("from_user_id", $client->user_id);

        return DataTables::of($advances)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "attachments", "status"])
            ->addColumn('action', function ($advance) {
                return '
                    <a href="' . route("admin.advances.edit", $advance->id) . '" class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap"><i class="fas fa-edit"></i></a>
                    <a href="javascript:;" onclick="deleteAdvance(' . $advance->id . ')" class="btn btn-sm btn-danger waves-effect waves-light text-nowrap"><i class="fas fa-trash"></i></a>
                ';
            })
            ->addColumn("attachments", function ($advance) {
                return view("admin.clients.partials.advances_attachments", ["item" => $advance]);
            })
            ->addColumn("status", function ($item) {
                $output = "";
                if ($item->status && Status::Approved->equals($item->status)) {
                    $output = ' <span class="badge bg-success">' . __($item->status->label()) . '</span>';
                } else if ($item->status && Status::Pending->equals($item->status)) {
                    $output = '<span class="badge bg-warning">' .  __($item->status->label()) . '</span>';
                } elseif ($item->status && Status::Rejected->equals($item->status)) {
                    $output =  '<span class="badge bg-danger">' . __($item->status->label()) . '</span>';
                } else {
                    $output =  '<span>-</span>';
                }
                return $output;
            })
            ->make(true);
    }

    public function loadInvoices($id)
    {
        $client = Client::findOrFail($id);
        $invoices = ShippingInvoice::query()->with("customShipping")->withCount("shippingExpenses")->where("user_id", $client->user_id);
        return DataTables::of($invoices)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "status", "attachments"])
            ->addColumn('action', function ($invoice) {
                return view("admin.clients.partials.shipping_invoices_btn", ["item" => $invoice]);
            })->addColumn("attachments", function ($invoice) {
                return view("admin.clients.partials.shipping_invoices_attachments", ["item" => $invoice]);
            })
            ->editColumn("status", function ($invoice) {
                if ($invoice->status == ShippingInvoiceStatus::Pending) {
                    return "<span class='badge bg-warning'>" . __("Pending") . "</span>";
                } else if ($invoice->status == ShippingInvoiceStatus::Approved) {
                    return "<span class='badge bg-success'>" . __("Approved") . "</span>";
                } else if ($invoice->status == ShippingInvoiceStatus::Rejected) {
                    return "<span class='badge bg-danger'>" . __("Rejected") . "</span>";
                } else {
                    return "-";
                }
            })->addColumn("shipping_expenses_count", function ($invoice) {
                return @$invoice->shipping_expenses_count;
            })->addColumn("policy_number", function ($invoice) {
                return @$invoice->customShipping->policy_number;
            })->addColumn("code", function ($invoice) {
                return @$invoice->customShipping->code;
            })
            ->addColumn("packages_count", function ($invoice) {
                return @$invoice->customShipping->packages_count;
            })
            ->addColumn("company_name", function ($invoice) {
                return @$invoice->customShipping->company->name;
            })
            ->addColumn("from_country", function ($invoice) {
                return @$invoice->customShipping->fromCountry->name;
            })
            ->addColumn("total_amount", function ($invoice) {
                return @$invoice->total_amount;
            })
            ->addColumn("total_revenue", function ($invoice) {
                if (is_admin()) {
                    return @$invoice->total_revenue;
                }
            })
            ->editColumn("created_at", function ($invoice) {
                return $invoice->created_at->toDateString();
            })
            ->make(true);
    }
    public function loadDocuments($id)
    {
        $client = Client::findOrFail($id);
        $documents = DocumentType::withCount("documents")
            ->whereHas("users", function ($inner) use ($client) {
                $inner->where("users.id", $client->user_id);
            });
        return DataTables::of($documents)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "documents_count", "document_type"])
            ->addColumn('action', function ($item) use ($client) {
                return '<a href="' . route('admin.documents.typesPage', ['id' => $item->id]) .  '"class="text-info"><i class="fas fa-eye"></i></a>';
            })
            ->addColumn("document_type", function ($item) use ($client) {
                return '<a href="' . route('admin.documents.typesPage', ['id' => $item->id]) . '">' . $item->name . '</a>';
            })
            ->addColumn("documents_count", function ($item) {
                return $item->documents_count;
            })
            ->make(true);
    }
    public function loadAllDocuments($id)
    {
        $client = Client::findOrFail($id);
        $documents = Document::with("documentType")
            ->whereHas("documentType.users", function ($inner) use ($client) {
                $inner->where("users.id", $client->user_id);
            });
        return DataTables::of($documents)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "attachments", "status", "document_type"])
            ->addColumn('action', function ($document) {
                return '
                    <a href="' . route("admin.documents.edit", $document->id) . '" class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap"><i class="fas fa-edit"></i></a>
                    <a href="javascript:;" onclick="deleteDocument(' . $document->id . ')" class="btn btn-sm btn-danger waves-effect waves-light text-nowrap"><i class="fas fa-trash"></i></a>
                ';
            })
            ->addColumn("attachments", function ($advance) {
                return view("admin.clients.partials.documents_attachments", ["item" => $advance]);
            })
            ->addColumn("document_type", function ($item) {
                return $item->documentType->name;
            })
            ->addColumn("status", function ($item) {

                return $item->remaining_days;
            })
            ->make(true);
    }
    public function loadCustomShippings($id)
    {
        $client = Client::findOrFail($id);
        $customShippings = CustomShipping::where("client_id", $client->id)->get();
        return DataTables::of($customShippings)
            ->addIndexColumn()
            ->rawColumns(["index", "action", "status"])
            ->addColumn('action', function ($invoice) {
                return view("admin.clients.partials.custom_shippings_btn", ["item" => $invoice]);
                //         '
                //     <a href="' . route("admin.custom_shipping_invoices.edit", $invoice->id) . '" class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap"><i class="fas fa-eye"></i></a>
                //     <a href="' . route('admin.custom_shipping_invoices.exportPdf', $invoice->id) . '"  target="_blank" class="btn btn-sm btn-success waves-effect waves-light text-nowrap"><i class="fas fa-print"></i></a>
                // ';
            })->editColumn("status", function ($invoice) {
                if ($invoice->status == CustomShippingStatus::ASSIGNED) {
                    return "<span class='badge bg-danger'>" . __("Assigned") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::FULL_DELIVERED) {
                    return "<span class='badge bg-primary'>" . __("Full Delivered") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::ThroughShipping) {
                    return "<span class='badge bg-dark'>" . __($invoice->status->label()) . "</span>";
                } else if ($invoice->status == CustomShippingStatus::PARTIAL_DELIVERED) {
                    return "<span class='badge bg-info'>" . __("Partial Delivered") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::INSPECTED) {
                    return "<span class='badge bg-warning'>" . __("Inspected") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::RELEASED) {
                    return "<span class='badge bg-success'>" . __("Released") . "</span>";
                } else if ($invoice->status == CustomShippingStatus::Distributed) {
                    return "<span class='badge bg-secondary'>" . __($invoice->status->label()) . "</span>";
                } else {
                    return "-";
                }
            })
            ->addColumn("company_name", function ($invoice) {
                return @$invoice->company->name;
            })
            ->addColumn("from_country", function ($invoice) {
                return @$invoice->fromCountry->name;
            })
            ->editColumn("created_at", function ($shipping) {
                return $shipping->created_at->toDateString();
            })
            ->make(true);
    }

    public function exportAdvancesToPdf($id)
    {
        $client = Client::findOrFail($id);
        $logo = Setting::where('key', 'logo')->first();
        $advances = Advance::where("from_user_id", $client->user_id)->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.clients.exports.export_advances', [
            'advances' => $advances,
            'logo' => $logo,
            'client' => $client
        ]);

        $pdf->Output(time() . "_advances.pdf", 'D');
    }
    public function exportCustomShippingsToPdf($id)
    {
        $client = Client::findOrFail($id);
        $logo = Setting::where('key', 'logo')->first();
        $shippings = CustomShipping::with("assignedTo")->where("client_id", $id)->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.clients.exports.export_custom_shippings', [
            'shippings' => $shippings,
            'logo' => $logo,
            'client' => $client
        ]);

        $pdf->Output(time() . "_shippings.pdf", 'D');
    }
}
