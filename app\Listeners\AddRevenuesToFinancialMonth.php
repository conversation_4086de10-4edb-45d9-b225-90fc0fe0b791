<?php

namespace App\Listeners;

use App\Models\FinancialMonth;
use App\Events\NewRevenueAddedEvent;
use App\Constants\FinancialMonthStatus;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class AddRevenuesToFinancialMonth
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewRevenueAddedEvent $event): void
    {
        $current_month = FinancialMonth::where('status', FinancialMonthStatus::Opened)->first();
        $current_month->total_revenues += $event->amount;
        $current_month->save();
    }
}
