<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserAnyPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permission): Response
    {
        if (Auth::check()) {
            if (auth("web")->user()->IsSuperAdmin == 1) {
                return $next($request);
            }

            if (!auth("web")->user()->canAny($permission)) {
                //check if units or groups
                abort(403);
            }
        }
        return $next($request);
    }
}
