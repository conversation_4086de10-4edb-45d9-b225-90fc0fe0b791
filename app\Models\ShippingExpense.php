<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShippingExpense extends Model
{
    protected $fillable = [
        "shipping_expense_type_id",
        "shipping_invoice_id",
        "amount",
        "notes"
    ];

    public function shippingExpenseType(): BelongsTo
    {
        return $this->belongsTo(ShippingExpenseType::class);
    }
    public function shippingInvoice(): BelongsTo
    {
        return $this->belongsTo(ShippingInvoice::class);
    }
}
