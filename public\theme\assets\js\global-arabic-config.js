/**
 * Global Arabic Configuration
 * This file provides global configuration for Arabic translations
 * Include this file in your main layout to apply Arabic translations site-wide
 */

"use strict";

// Global configuration object
window.ArabicConfig = {
    // Current locale
    locale: document.documentElement.lang || "en",

    // Check if current locale is Arabic
    isArabic: function () {
        return this.locale === "ar";
    },

    // Select2 Arabic language configuration
    select2Language: {
        noResults: function () {
            return "لم يتم العثور على نتائج";
        },
        searching: function () {
            return "جاري البحث...";
        },
        loadingMore: function () {
            return "جاري تحميل المزيد من النتائج...";
        },
        maximumSelected: function (args) {
            return "يمكنك اختيار " + args.maximum + " عناصر فقط";
        },
        inputTooShort: function (args) {
            var remainingChars = args.minimum - args.input.length;
            return "يرجى إدخال " + remainingChars + " أحرف أو أكثر";
        },
        inputTooLong: function (args) {
            var overChars = args.input.length - args.maximum;
            return "يرجى حذف " + overChars + " أحرف";
        },
        errorLoading: function () {
            return "لا يمكن تحميل النتائج";
        },
    },

    // File input Arabic texts
    fileInputTexts: {
        noFileChosen: "لم يتم اختيار ملف",
        chooseFile: "اختر ملف",
        multipleFiles: function (count) {
            return count + " ملفات محددة";
        },
    },

    // Initialize Select2 with Arabic support
    initSelect2: function (selector, options) {
        selector = selector || ".select2";
        options = options || {};

        var defaultOptions = {
            placeholder: "اختر...",
            allowClear: true,
        };

        if (this.isArabic()) {
            defaultOptions.language = this.select2Language;
        }

        // Merge with custom options
        var finalOptions = $.extend(true, {}, defaultOptions, options);

        $(selector).select2(finalOptions);
    },

    // Initialize file inputs with Arabic support
    initFileInputs: function (selector) {
        if (!this.isArabic()) return;

        selector = selector || 'input[type="file"]';
        var self = this;

        $(selector).each(function () {
            var $input = $(this);

            // Skip if already customized
            if ($input.hasClass("arabic-customized")) {
                return;
            }

            $input.addClass("arabic-customized");

            var $wrapper = $(
                '<div class="file-input-wrapper position-relative d-flex align-items-center"></div>'
            );
            var $customButton = $(
                '<button type="button" class="btn btn-outline-primary file-input-button">' +
                    self.fileInputTexts.chooseFile +
                    "</button>"
            );
            var $fileName = $(
                '<span class="file-name me-2 text-muted">' +
                    self.fileInputTexts.noFileChosen +
                    "</span>"
            );

            $input.wrap($wrapper);
            $input.addClass("d-none");
            $input.after($customButton).after($fileName);

            $customButton.on("click", function () {
                $input.click();
            });

            $input.on("change", function () {
                var files = this.files;
                if (files.length > 0) {
                    if (files.length === 1) {
                        $fileName.text(files[0].name);
                    } else {
                        $fileName.text(
                            self.fileInputTexts.multipleFiles(files.length)
                        );
                    }
                    $fileName.removeClass("text-muted").addClass("text-dark");
                } else {
                    $fileName.text(self.fileInputTexts.noFileChosen);
                    $fileName.removeClass("text-dark").addClass("text-muted");
                }
            });
        });
    },

    // Initialize all Arabic customizations
    init: function () {
        var self = this;

        $(document).ready(function () {
            // Initialize Select2 with Arabic support
            self.initSelect2();

            // Initialize file inputs with Arabic support
            self.initFileInputs();

            // Watch for dynamically added elements
            var observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === "childList") {
                        mutation.addedNodes.forEach(function (node) {
                            if (node.nodeType === 1) {
                                // Element node
                                var $node = $(node);

                                // Check for new select2 elements
                                var $newSelects = $node
                                    .find(".select2")
                                    .add($node.filter(".select2"));
                                if ($newSelects.length > 0) {
                                    self.initSelect2($newSelects);
                                }

                                // Check for new file inputs
                                var $newFileInputs = $node
                                    .find('input[type="file"]')
                                    .add($node.filter('input[type="file"]'));
                                if ($newFileInputs.length > 0) {
                                    self.initFileInputs($newFileInputs);
                                }
                            }
                        });
                    }
                });
            });

            // Start observing
            observer.observe(document.body, {
                childList: true,
                subtree: true,
            });
        });
    },
};

// Auto-initialize when script loads
window.ArabicConfig.init();

// Make it available globally
window.appLocale = window.ArabicConfig.locale;
