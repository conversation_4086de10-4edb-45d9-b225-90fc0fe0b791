<?php

namespace App\Models;

use Illuminate\Support\Facades\File;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Attachment extends Model
{
    protected $fillable = [
        "attachmentable_id",
        "attachmentable_type",
        "file_path",
        "type",
        "mime_type",
        "filename",
        "original_filename",
        "visible_to"
    ];

    public function attachmentable(): MorphTo
    {
        return $this->morphTo();
    }

    public function delete()
    {
        // Check if file exists
        if ($this->file_path && File::exists(public_path($this->file_path))) {
            File::delete(public_path($this->file_path));
        }
        // Proceed to delete the database record
        return parent::delete();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
}
