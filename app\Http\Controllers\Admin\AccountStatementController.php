<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Client;
use App\Models\Advance;
use App\Models\Revenue;
use App\Models\Setting;
use App\Constants\Status;
use App\Constants\UserType;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\ShippingInvoice;
use App\Http\Controllers\Controller;
use App\Constants\ShippingInvoiceStatus;
use stdClass;

class AccountStatementController extends Controller
{
    public function export(Request $request, $user_id)
    {
        $request->validate([
            "start_from" => "required|date",
            "end_to" => "required|date|after_or_equal:start_from"
        ]);
        try {
            if (!is_admin()) {
                if ((is_broker() || is_client()) && $user_id != auth("web")->id()) return abort(404);
                else if (is_sub_client()) {
                    $client = Client::findOrFail(auth("web")->user()->client_id);
                    if ($client->user_id != $user_id) {
                        return abort(404);
                    }
                }
            }
            $logo = Setting::where("key", "logo")->first()->value;

            $lang = request()->query("lang") ?? "ar";
            if ($lang != "ar" && $lang != "en") {
                return back();
            }
            app()->setLocale($lang);
            $data["logo"] = $logo;

            $start_from = $request->start_from;
            $end_to = $request->end_to;

            $data["start_from"] = $start_from;
            $data["end_to"] = $end_to;
            $data["client"] = User::with("client")->findOrFail($user_id)->client;
            // Fetch invoices
            $invoices = ShippingInvoice::with("customShipping", "shippingExpenses")->where([
                "user_id" => $user_id,
                "status" => ShippingInvoiceStatus::Approved->value
            ])->whereBetween("date", [$start_from, $end_to])
                ->get()
                ->map(function ($invoice) {
                    $obj = new stdClass();
                    $obj->approved_at = $invoice->date;
                    $obj->id = "invoice_" . $invoice->id;
                    $obj->sort_date = $invoice->customShipping?->date;
                    $obj->type = "Debtor";
                    $obj->credit = 0;
                    $obj->debit = $invoice->total_amount;
                    $obj->current_balance = $invoice->current_balance;
                    $obj->policy_number = $invoice->customShipping?->policy_number;
                    return $obj;
                });

            // Fetch advances
            $advances = Advance::where([
                "from_user_id" => $user_id,
                "status" => Status::Approved->value
            ])->whereBetween("approved_at", [$start_from, $end_to])
                ->get()
                ->map(function ($advance) {
                    $obj = new stdClass();
                    $obj->approved_at = $advance->approved_at;
                    $obj->id = "advance_" . $advance->id;
                    $obj->sort_date = $advance->date;
                    $obj->type = "Creditor";
                    $obj->credit = $advance->amount;
                    $obj->debit = 0;
                    $obj->current_balance = $advance->current_balance;
                    return $obj;
                });

            $revenues = Revenue::where(['client_id' => $data["client"]->id])
                ->whereBetween("date", [$start_from, $end_to])
                ->get()->map(function ($revenue) {
                    $obj = new stdClass();
                    $obj->id = "revenue_" . $revenue->id;
                    $obj->sort_date = $revenue->date;
                    $obj->approved_at = $revenue->date;
                    $obj->type = "Revenue";
                    $obj->credit = 0;
                    $obj->debit = $revenue->amount;
                    $obj->current_balance = $revenue->current_balance;
                    return $obj;
                });
            $combined = $invoices->merge($advances)->merge($revenues);
            $sorted = $combined->sortBy('approved_at');

            $payments = $sorted->values();

            $data["payments"] = $payments;
            return view("admin.account_statements.print", $data);
        } catch (\Throwable $th) {
            throw $th;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    public function exportBroker(Request $request, $user_id)
    {
        $request->validate([
            "start_from" => "required|date",
            "end_to" => "required|date|after_or_equal:start_from"
        ]);
        try {
            $logo = Setting::where("key", "logo")->first()->value;

            $lang = request()->query("lang") ?? "ar";
            if ($lang != "ar" && $lang != "en") {
                return back();
            }
            app()->setLocale($lang);
            $data["logo"] = $logo;

            $data["user"] = User::findOrFail($user_id);
            $start_from = $request->start_from;
            $end_to = $request->end_to;

            $data["start_from"] = $start_from;
            $data["end_to"] = $end_to;
            // Fetch invoices
            $invoices = ShippingInvoice::with("customShipping", "shippingExpenses")->where([
                "user_id" => $user_id,
                "status" => ShippingInvoiceStatus::Approved->value
            ])->whereBetween("date", [$start_from, $end_to])
                ->get()
                ->map(function ($invoice) {
                    $obj = new stdClass();
                    $obj->approved_at = $invoice->date;
                    $obj->id = "invoice_" . $invoice->id;
                    $obj->sort_date = $invoice->customShipping?->date;
                    $obj->type = "Creditor";
                    $obj->credit = $invoice->total_amount;
                    $obj->current_balance = $invoice->current_balance;
                    $obj->policy_number = $invoice->customShipping?->policy_number;
                    $obj->debit = 0;
                    return $obj;
                });
            // Fetch advances
            $advances = Advance::where([
                "to_user_id" => $user_id,
                "status" => Status::Approved->value
            ])->whereBetween("approved_at", [$start_from, $end_to])
                ->get()
                ->map(function ($advance) {
                    $obj = new stdClass();
                    $obj->approved_at = $advance->approved_at;
                    $obj->id = "advance_" . $advance->id;
                    $obj->sort_date = $advance->date;
                    $obj->type = "Debtor";
                    $obj->credit = 0;
                    $obj->debit = $advance->amount;
                    $obj->current_balance = $advance->current_balance;
                    return  $obj;
                });
            $combined = $invoices->merge($advances);
            $sorted = $combined->sortBy('approved_at');
            $payments = $sorted->values();
            $data["payments"] = $payments;
            return view("admin.account_statements.print-broker", $data);
        } catch (\Throwable $th) {
            throw $th;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
}
