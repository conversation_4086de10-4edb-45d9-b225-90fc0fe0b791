<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FinancialYear extends Model
{
    protected $fillable = [
        "name",
        "description",
        "start_from",
        "end_to",
        "status",
        "created_by",
        "updated_by"
    ];

    public function financialMonths(): HasMany
    {
        return $this->hasMany(FinancialMonth::class);
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "created_by");
    }
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, "updated_by");
    }
}
