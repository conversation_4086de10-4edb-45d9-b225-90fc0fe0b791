<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId("shipping_invoice_id")->nullable()->constrained("shipping_invoices")->onDelete('cascade');
            $table->foreignId("shipping_expense_type_id")->nullable()->constrained("shipping_expense_types")->onDelete('cascade');
            $table->decimal("amount", 10, 2)->default(0.00);
            $table->text("notes")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_expenses');
    }
};
