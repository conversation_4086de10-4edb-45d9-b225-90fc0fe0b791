<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Notification;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class NotificationsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Notifications");
        return view('admin.notifications.index', $data);
    }
    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;

            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = Notification::where(['user_id' => auth("web")->id()])->latest('id');
            $TotalCount = $query->count();
            $users = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $view = view("admin.notifications.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        try {
            $notification = Notification::findOrFail($id);
            if ($notification->read_at == null) {
                $notification->read_at = now();
                $notification->save();
            }
            return redirect()->route($notification->route,  json_decode($notification->routeParams));
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return back();
        }
    }

    public function markAsRead()
    {
        try {
            DB::table("notifications")
                ->whereNull("read_at")
                ->update(['read_at' => now()]);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return back();
        } catch (\Throwable $th) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return back();
        }
    }
}
