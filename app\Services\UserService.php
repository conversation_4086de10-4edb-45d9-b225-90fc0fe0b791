<?php

namespace App\Services;

use Exception;
use App\Models\User;
use App\Constants\UserType;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\Admin\User\CreateUserRequest;
use App\Http\Requests\Admin\User\UpdateUserRequest;

class UserService
{
    /**
     * Create new User 
     * 
     * @param CreateUserRequest $request
     * 
     * @return User
     */
    public function createUser(CreateUserRequest $request)
    {
        try {
            $user = User::create([
                'name_ar' => $request->name_ar,
                'name_en' => $request->name_en,
                'username' => $request->username,
                "email" => $request->email,
                "password" => bcrypt($request->password),
                "phone" => $request->phone,
                "address" => $request->address,
                "IsSuperAdmin" => $request->has("IsSuperAdmin"),
                "status" => $request->status,
                "type" => $request->type,
                "client_id" => $request->client_id,
            ]);

            if (isset($request->email_verified_at)) {
                $user->email_verified_at = $request->email_verified_at;
            }
            if ($request->roles != null && count($request->roles) > 0) {
                $user->assignRole($request->roles);
            }
            return $user;
        } catch (\Throwable $th) {
            throw $th;
        }
    }
    /**
     * Update User 
     * 
     * @param Request $request
     * 
     * @param User $user
     * 
     * @return bool
     */
    public function updateUser(UpdateUserRequest $request, User $user)
    {
        $updated = false;
        try {
            $data = $request->validated();
            if ($data["type"] != UserType::SubClient->value) {
                $data["client_id"] = null;
            }
            $user->update([
                'name_ar' => $data["name_ar"] ?? "",
                'name_en' => $data["name_en"] ?? "",
                'username' => $data["username"] ?? "",
                "email" => $data["email"],
                "phone" => $data["phone"] ?? "",
                "address" => $data["address"] ?? "",
                "IsSuperAdmin" => $request->has("IsSuperAdmin"),
                "status" => $data["status"],
                "type" => $data["type"],
                "client_id" => $data["client_id"],
                // "type" => UserType::Employee
            ]);

            if (isset($request->password)) {
                $user->password = bcrypt($request->password);
                $user->save();
            }
            if ($request->roles != null && count($request->roles) > 0) {
                $user->syncRoles($request->roles);
            }
            $updated = true;
        } catch (\Throwable $th) {
            throw $th;
        }

        return $updated;
    }

    public function updateProfile(array $data)
    {
        try {
            $user = User::findOrFail(auth('web')->id());
            $user->update([
                "username" => $data["username"],
                "name_ar" => $data["name_ar"],
                "name_en" => $data["name_en"],
                "email" => $data["email"],
                "phone" => $data["phone"],
                "address" => $data["address"],
            ]);
            if (isset($data["avatar"])) {
                $this->uploadAvatar($data["avatar"], $user);
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }
    public function uploadAvatar(UploadedFile $file, $user)
    {
        $mime_type = $file->getClientOriginalExtension();
        $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

        $path         = "uploads/users/avatars";
        $uploadPath   = public_path($path);
        $file->move($uploadPath, $uniqueName);
        $file_path = $path . "/" . $uniqueName;

        $user->avatar()->create([
            "file_path" => $file_path
        ]);
    }

    public function changePassword(array $data)
    {
        try {
            $user = User::findOrFail(auth('web')->id());
            $user->password = bcrypt($data["new_password"]);
            $user->save();
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
