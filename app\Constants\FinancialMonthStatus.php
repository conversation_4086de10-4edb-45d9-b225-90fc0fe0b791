<?php

namespace App\Constants;

enum FinancialMonthStatus: int
{
    case Closed = 1;
    case Opened = 2;
    case Archived = 3;

    public function label(): string
    {
        return match ($this) {
            self::Closed => "Closed",
            self::Opened => "Opened",
            self::Archived => "Archived",
        };
    }

    public static function availableTypes(): array
    {
        return  [
            "Closed" => self::Closed,
            "Opened" => self::Opened,
            "Archived" => self::Archived,
        ];
    }
    /**
     * Compare the enum with a value.
     *
     * @param int|FinancialMonthStatus $value
     * @return bool
     */
    public function equals(int|FinancialMonthStatus $value): bool
    {
        return $value instanceof self ? $this === $value : $this->value === $value;
    }
}
