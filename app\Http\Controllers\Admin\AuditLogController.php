<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\AuditLog;
use App\Http\Controllers\Controller;

class AuditLogController extends Controller
{
    public function index()
    {
        $data["title"] = __("Audit Logs");
        $data["sub_title"] = __("Audit Logs");
        return view('admin.logs.audit_logs.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = AuditLog::with("user")->when($search, function ($query) use ($search) {
                $query->whereHas("user", function ($q) use ($search) {
                    $q->where("name", "LIKE", "%" . $search . "%")
                        ->orWhere("email", "LIKE", "%" . $search . "%");
                })->orWhere("ip_address", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            if ($orderBy == "user_name") {
                $query = $query
                    ->join('users as u', 'u.id', '=', 'audit_logs.user_id')
                    ->orderBy('u.name', $direction)->select("audit_logs.*");
            } else {
                $query = $query->orderBy($orderBy, $direction);
            }

            $users = $query->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = min($skip + $RowsPerPage, $TotalCount);
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.logs.audit_logs.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            return $e;
            $view = view("admin.components.something_went_wrong")->render();
            return response($view);
        }
    }
}
