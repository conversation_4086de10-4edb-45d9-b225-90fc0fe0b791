<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\FinancialMonth;
use Illuminate\Console\Command;
use App\Constants\FinancialMonthStatus;

class CloseCurrentMonthAndOpenNewMonthCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:open-new-month';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = now();
        $after_month = now()->addMonth()->toDateString();
        $current_month = FinancialMonth::where("status", FinancialMonthStatus::Opened)->first();
        if (!$current_month) {
            $this->error("No current financial month found.");
            return;
        }
        $new_month = FinancialMonth::where("financial_year_id", $current_month->financial_year_id)
            ->where("start_from", ">", $current_month->start_from)
            ->orderBy("start_from")
            ->first();
        if (!$new_month) {
            $this->error("No next financial month found.");
            return;
        }
        $end_date = Carbon::parse($current_month->end_to);

        if ($end_date->lessThanOrEqualTo($today)) {
            $current_month->status = FinancialMonthStatus::Archived;
            $current_month->end_balance = $current_month->start_balance + $current_month->total_revenues - ($current_month->total_expenses + $current_month->withdrawn_revenues);
            $current_month->save();

            $new_month->status = FinancialMonthStatus::Opened;
            $new_month->start_balance = $current_month->end_balance;
            $new_month->save();
        }
        //calculate the end_balance and start balance

    }
}
