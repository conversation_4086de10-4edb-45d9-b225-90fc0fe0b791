<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CheckUserPermissions
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        if (Auth::check()) {
            if (auth("web")->user()->IsSuperAdmin == 1) {
                return $next($request);
            }

            if (!auth("web")->user()->can($permission)) {
                //check if units or groups
                abort(403);
            }
        }
        return $next($request);
    }
}
