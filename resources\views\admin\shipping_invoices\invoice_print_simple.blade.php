@extends('layouts.print')
@push('css')
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
            color: #333 !important;
            margin: 0 !important;
            padding: 20px !important;
        }
        
        .invoice-header {
            display: flex !important;
            justify-content: space-between !important;
            margin-bottom: 20px !important;
            padding-bottom: 15px !important;
            border-bottom: 2px solid #333 !important;
        }
        
        .company-info img {
            width: 80px !important;
            height: 80px !important;
            margin-bottom: 10px !important;
        }
        
        .company-name {
            font-size: 18px !important;
            font-weight: bold !important;
            margin-bottom: 5px !important;
        }
        
        .invoice-title {
            font-size: 20px !important;
            font-weight: bold !important;
            text-align: right !important;
        }
        
        .client-section {
            display: flex !important;
            justify-content: space-between !important;
            margin-bottom: 20px !important;
        }
        
        .client-info, .shipping-info {
            width: 48% !important;
        }
        
        .section-title {
            font-weight: bold !important;
            font-size: 14px !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #ddd !important;
            padding-bottom: 5px !important;
        }
        
        .info-item {
            margin-bottom: 5px !important;
        }
        
        .invoice-table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin-bottom: 20px !important;
        }
        
        .invoice-table th,
        .invoice-table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
            text-align: left !important;
        }
        
        .invoice-table th {
            background-color: #f5f5f5 !important;
            font-weight: bold !important;
        }
        
        .total-row {
            background-color: #f0f0f0 !important;
            font-weight: bold !important;
        }
        
        .text-center {
            text-align: center !important;
        }
        
        .text-right {
            text-align: right !important;
        }
    </style>
@endpush

@section('content')
<div class="invoice-container">
    <!-- Header -->
    <div class="invoice-header">
        <div class="company-info">
            @php
                $logoSetting = get_setting('logo');
            @endphp
            @if($logoSetting)
                <img src="{{ asset($logoSetting) }}" alt="Company Logo">
            @endif
            <div class="company-name">{{ get_setting('company_name') ?? get_site_name() }}</div>
            <div>{{ get_setting('location') ?? get_setting('company_address') }}</div>
            <div>{{ get_setting('phone') ?? get_setting('company_phone') }}</div>
        </div>
        <div class="invoice-info">
            <div class="invoice-title">فاتورة رقم #{{ $invoice->invoice_number }}</div>
            <div>تاريخ الفاتورة: {{ $invoice->created_at->format('d/m/Y') }}</div>
            <div>تاريخ الطباعة: {{ now()->format('d/m/Y') }}</div>
        </div>
    </div>

    <!-- Client Information -->
    <div class="client-section">
        <div class="client-info">
            <div class="section-title">معلومات العميل</div>
            <div class="info-item"><strong>الاسم:</strong> {{ $invoice->user->name }}</div>
            <div class="info-item"><strong>الهاتف:</strong> {{ $invoice->user->phone }}</div>
            <div class="info-item"><strong>البريد:</strong> {{ $invoice->user->email }}</div>
            @if($invoice->user->address)
                <div class="info-item"><strong>العنوان:</strong> {{ $invoice->user->address }}</div>
            @endif
        </div>
        
        <div class="shipping-info">
            <div class="section-title">تفاصيل الشحنة</div>
            @if (is_admin() || is_client())
                <div class="info-item"><strong>العميل:</strong> {{ @$invoice->customShipping?->client->name }}</div>
            @endif
            <div class="info-item"><strong>الشركة:</strong> {{ @$invoice->customShipping?->company->name }}</div>
            <div class="info-item"><strong>رقم البوليصة:</strong> {{ $invoice->customShipping?->policy_number }}</div>
            @if (is_admin() || is_client())
                <div class="info-item"><strong>الكود:</strong> {{ $invoice->customShipping?->code }}</div>
            @endif
            <div class="info-item"><strong>عدد الطرود:</strong> {{ $invoice->customShipping?->packages_count }}</div>
            <div class="info-item"><strong>الوزن:</strong> {{ $invoice->customShipping?->total_weight }} كجم</div>
        </div>
    </div>

    <!-- Invoice Items -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 45%;">اسم المصروف</th>
                <th style="width: 15%;">المبلغ</th>
                <th style="width: 35%;">ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($invoice->shippingExpenses as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        @if (app()->getLocale() == 'ar')
                            {{ $item->shippingExpenseType->name_ar }}
                        @else
                            {{ $item->shippingExpenseType->name_en }}
                        @endif
                    </td>
                    <td class="text-center">{{ number_format($item->amount, 2) }}</td>
                    <td>{{ $item->notes ?? '-' }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr class="total-row">
                <td colspan="2" class="text-center"><strong>المجموع الكلي</strong></td>
                <td class="text-center"><strong>{{ number_format($invoice->total_amount, 2) }}</strong></td>
                <td></td>
            </tr>
        </tfoot>
    </table>

    <!-- Notes -->
    @if($invoice->notes)
        <div style="margin-bottom: 20px;">
            <strong>ملاحظات:</strong> {{ $invoice->notes }}
        </div>
    @endif

    @if (is_admin() || is_client())
        <!-- Payment Info -->
        <div style="font-size: 10px; margin-top: 30px;">
            <div style="display: flex; justify-content: space-between;">
                <div>
                    <strong>تعليمات الدفع:</strong><br>
                    يرجى تحرير جميع الشيكات باسم: السيد/ إبراهيم البدري إبراهيم مجاهد<br>
                    المدير التنفيذي لشركة CLEARO<br>
                    شكراً لتعاملكم معنا!
                </div>
                <div>
                    <strong>بيانات التحويل البنكي:</strong><br>
                    اسم العميل: إبراهيم البدري إبراهيم مجاهد<br>
                    رقم الآيبان: KW05KFHO0000000000061320007982<br>
                    رقم الحساب: 061320007982
                </div>
            </div>
        </div>
    @endif
</div>

@include('layouts.export-partials.export_invoice_footer')
@endsection
