<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\Client;
use App\Models\HsSheet;
use App\Constants\UserType;
use Illuminate\Support\Str;
use App\Exports\UsersExport;
use App\Services\PdfService;
use Illuminate\Http\Request;
use App\Services\UserService;
use App\Constants\CrudMessage;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\Permission\Models\Permission;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Admin\User\CreateUserRequest;
use App\Http\Requests\Admin\User\UpdateUserRequest;
use App\Http\Requests\Admin\User\UpdateProfileRequest;
use App\Http\Requests\Admin\User\ChangePasswordRequest;

class UserController extends Controller
{
    protected $userService;
    protected $pdfService;

    public function __construct()
    {
        $this->userService = new UserService();
        $this->pdfService = new PdfService();
        $this->middleware('is_able:Create User')->only(['create', 'store']);
        $this->middleware('is_able:View Users')->only(['index', 'view']);
        $this->middleware('is_able:Update User')->only(['edit', 'update']);
        $this->middleware('is_able:Delete User')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $type = request()->query("type") ?? null;

        if ($type && UserType::Broker->equals($type)) {
            $data["title"] = __("Brokers");
            $data["sub_title"] = __("Brokers List");
        } else {
            $data["title"] = __("Users");
            $data["sub_title"] = __("Users List");
        }
        $data["type"] = $type;
        return view('admin.users.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";
            $userType = $request->UserType;

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = User::when($search, function ($query) use ($search) {
                $query->where("name", "LIKE", "%" . $search . "%")
                    ->orWhere("email", "LIKE", "%" . $search . "%");
            })->when($userType, function ($query) use ($userType) {
                $query->where("type", $userType);
            })
                ->where('id', '!=', auth("web")->id())
                ->where("IsSuperAdmin", "!=", 1);
            $TotalCount = $query->count();
            $users = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.users.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Users");
        $data["sub_title"] = __("Add New User");
        $data["roles"] = Role::get();

        $data["type"] = request()->query("type");
        $data["client_id"] = request()->query("client_id");

        $data["clients"] = Client::get();
        return view('admin.users.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateUserRequest $request)
    {
        try {
            if (!$request->type) {
                // $request["type"] = UserType::Employee->value;
            }
            $user_created = $this->userService->createUser($request);
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            // return $e;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    public function permissions($id)
    {
        try {
            if (!is_admin() && !auth()->user()->can("Assign Permissions")) {
                return back();
            }
            $data["title"] = __("Users");
            $data["sub_title"] = __("Give Permissions To User");
            $user = User::with("permissions")->findOrFail($id);
            $data["user"] = $user;
            $data["permissions"] = Permission::get(["id", "name", "group"])->groupBy("group");
            return view('admin.users.permissions', $data);
        } catch (\Throwable $th) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    public function assignPermission(Request $request, $id)
    {
        try {
            $permissions = $request->permissions;
            $user = User::findOrFail($id);

            $user->syncPermissions($permissions);
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (\Throwable $th) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('admin.users.profile');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $data["title"] = __("Users");
            $data["sub_title"] = __("Update User");

            $data["roles"] = Role::all();
            $data["user"] = User::findOrFail($id);
            $data["clients"] = Client::get();
            return view('admin.users.edit', $data);
        } catch (Exception $e) {
            session()->flash("error", $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $user_created = $this->userService->updateUser($request, $user);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            return $e;
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $user = User::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    // public function exportAllToPdf()
    // {
    //     $logo = "";
    //     $users = User::where('id', '!=', auth("web")->id())
    //         ->where("IsSuperAdmin", "!=", 1)->get();
    //     $mpdf = new \Mpdf\Mpdf([
    //         'mode'        => 'utf-8',
    //         'format'      => 'A4',
    //         'orientation' => 'landscape',
    //     ]);
    //     $mpdf->shrink_tables_to_fit = 0;
    //     $mpdf->autoScriptToLang         = true;
    //     $mpdf->autoLangToFont           = false;
    //     $mpdf->allow_charset_conversion = false;
    //     $mpdf->setAutoTopMargin = 'pad';
    //     $mpdf->setAutoBottomMargin  = 'pad';
    //     $mpdf->falseBoldWeight = 10;
    //     $mpdf->WriteHTML(view('admin.users.export_pdf', compact('users', 'logo', "mpdf")));
    //     $mpdf->Output(time() . "_users" . '.pdf', 'D');
    // }

    public function exportAllToPdf()
    {
        $logo = "";
        $users = User::where('id', '!=', auth("web")->id())->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.users.export_pdf', [
            'users' => $users,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_users.pdf", 'D');
    }

    public function exportAllToExcel()
    {
        return Excel::download(new UsersExport(), 'users.xlsx');
    }
    public function accountSetting()
    {
        $data = [];
        $data["title"] = __("My Profile");
        return view("admin.users.account_settings", $data);
    }
    public function updateAccount(UpdateProfileRequest $request)
    {
        try {
            $this->userService->updateProfile($request->validated());
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
    public function getChangePassword()
    {
        $data = [];
        $data["title"] = __("Change Password");
        return view("admin.users.change_password", $data);
    }
    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            $user = DB::table("users")->where("id", auth("web")->id())->first();
            if (!Hash::check($request["current_password"], $user->password)) {
                session()->flash("error", __(CrudMessage::isNotValid("password")));
                return redirect()->back();
            }
            $this->userService->changePassword($request->validated());
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
    public function testPage()
    {
        $client = new \GuzzleHttp\Client();

        $response = $client->request('POST', 'https://strowallet.com/api/stw-payments/', [
            'headers' => [
                'accept' => 'application/json',
            ],
            'query' => [
                'public_key' => "******************************",
                "amount_usd" => 5,
                "payer_name" => "Mahmoud",
                "payer_email" => "<EMAIL>",
                "success_url" => "https://www.google.com",
                "failed_url" => "https://www.google.com",
                "item" => "airpod aqua"
            ],
        ]);

        return $response->getBody();
    }
    public function testYajra()
    {
        $users = User::query();
        $hs_sheet = HsSheet::query();

        return DataTables::of($hs_sheet)
            ->addIndexColumn()
            ->rawColumns(["index", "action"])
            ->addColumn('action', function ($user) {
                return '
                    <a href="/users/' . $user->id . '/edit" class="btn btn-primary">Edit</a>
                    <a href="/users/' . $user->id . '/delete" class="btn btn-danger">Delete</a>
                ';
            })
            ->make(true);
    }
}
