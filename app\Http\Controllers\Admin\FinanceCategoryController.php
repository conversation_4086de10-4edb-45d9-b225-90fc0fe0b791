<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\FinanceCategory;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\PdfService;

class FinanceCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Finance Category");
        return view("admin.intialization.finance_categories.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = FinanceCategory::when($search, function ($query) use ($search) {
                $query
                    ->where("name", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.intialization.finance_categories.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name_ar" => "required",
            "name_en" => "required",
            "status" => "required"
        ]);
        try {
            $data = $request->only(['name_ar', 'name_en', "status"]);
            $data["created_by"] = auth("web")->id();
            FinanceCategory::create($data);
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = FinanceCategory::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "name_ar" => "required",
            "name_en" => "required",
            "status" => "required"
        ]);
        try {
            $data = $request->only(['name_ar', 'name_en', "status"]);
            $data["updated_by"] = auth("web")->id();
            $item = FinanceCategory::findOrFail($id);
            $item->update($data);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            FinanceCategory::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = "";
        $categories = DB::table("finance_categories AS c")
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_purchases FROM purchases GROUP BY finance_category_id) AS p"), function ($join) {
                $join->on("p.finance_category_id", "=", "c.id");
            })
            ->leftJoin(DB::raw("(SELECT finance_category_id, SUM(amount) as total_revenues FROM revenues GROUP BY finance_category_id) AS r"), function ($join) {
                $join->on("r.finance_category_id", "=", "c.id");
            })
            ->select(
                "c.id",
                "c.name",
                DB::raw('COALESCE(p.total_purchases, 0) as total_purchases'),
                DB::raw('COALESCE(r.total_revenues, 0) as total_revenues')
            )
            ->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.intialization.finance_categories.export_pdf', [
            'categories' => $categories,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_categories.pdf", 'D');
    }
}
