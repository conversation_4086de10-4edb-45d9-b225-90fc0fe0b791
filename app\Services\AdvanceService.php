<?php

namespace App\Services;

use App\Models\{Advance, ClientDebit, User, Wallet, WorkerDue};
use App\Constants\{AttachmentVisibleStatus, Status, UserType};
use App\Events\{NewExpenseAddedEvent, NewRevenueAddedEvent};
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Exception;

class AdvanceService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();

            $advance = Advance::create([
                'to_user_id'    => $data['to_user_id'] ?? null,
                'from_user_id'  => $data['from_user_id'] ?? null,
                'amount'        => $data['amount'],
                'date'          => $data['date'] ?? now(),
                'notes'         => $data['notes'] ?? null,
                'status'        => $data['status'] ?? Status::Pending->value,
                'created_by'    => auth('web')->id(),
            ]);

            $this->handleAttachments($advance, $data);

            if ($data['status'] == Status::Approved->value) {
                $this->handleApproval($advance, $data['type']);
                $this->handleApprovedAtDate($advance, $data['status']);
            }

            if (is_client() && !is_admin()) {
                $this->notifyAdminOnClientAction($advance, 'create');
            }

            DB::commit();
            return $advance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();

            $advance = Advance::findOrFail($id);
            $oldStatus = $advance->status?->value;
            $newStatus = (int)$data['status'];
            $amountDiff = $data['amount'] - $advance->amount;

            $advance->update([
                'to_user_id'    => $data['to_user_id'] ?? null,
                'from_user_id'  => $data['from_user_id'] ?? null,
                'amount'        => $data['amount'],
                'date'          => $data['date'] ?? now(),
                'notes'         => $data['notes'] ?? null,
                'status'        => $newStatus,
                'updated_by'    => auth('web')->id(),
            ]);

            $this->handleAttachments($advance, $data);

            if ($oldStatus != $newStatus || $amountDiff > 0) {
                $this->handleStatusChange($advance, $oldStatus, $newStatus, $amountDiff, $data['type']);
            }
            $this->handleApprovedAtDate($advance, $data['status']);
            DB::commit();
            return $advance;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $advance = Advance::findOrFail($id);
            $isEmployee = $advance->to_user_id != null;
            $userId = $isEmployee ? $advance->to_user_id : $advance->from_user_id;
            $walletChange = 0;
            if ($advance->status == Status::Approved) {
                $walletChange = -$advance->amount;
                if ($isEmployee) {
                    $this->updateWorkerDue($userId, $walletChange);
                    $wallet = $this->updateUserWallet($userId, $walletChange, !$isEmployee);
                    NewExpenseAddedEvent::dispatch($walletChange);
                } else {
                    $this->updateClientDebit($userId, $walletChange);
                    $wallet = $this->updateUserWallet($userId, $walletChange);
                    NewRevenueAddedEvent::dispatch($walletChange);
                }
            }
            $advance->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function handleAttachments($advance, array $data): void
    {
        if (!isset($data['attachments'])) return;

        foreach ($data['attachments'] as $file) {
            $original = $file->getClientOriginalName();
            $ext = $file->getClientOriginalExtension();
            $filename = strtolower(Str::uuid() . '.' . $ext);
            $path = 'uploads/advances';

            $file->move(public_path($path), $filename);

            $advance->attachments()->create([
                'file_path' => "$path/$filename",
                'filename' => $filename,
                'original_filename' => $original,
                'mime_type' => $ext,
                'visible_to' => $data['type'] == UserType::Broker->value
                    ? AttachmentVisibleStatus::Employee
                    : AttachmentVisibleStatus::Client,
            ]);
        }
    }
    private function handleApproval($advance, $userType): void
    {
        if ($userType == UserType::Broker->value) {
            $this->updateWorkerDue($advance->to_user_id, $advance->amount);
            $wallet = $this->updateUserWallet($advance->to_user_id, $advance->amount, false);
            $advance->current_balance = $wallet->amount;
            $advance->save();

            NewExpenseAddedEvent::dispatch($advance->amount);
            $this->createNotificationToWorkerAfterAdvancePaid($advance);
        } elseif ($userType == UserType::Client->value) {
            $this->updateClientDebit($advance->from_user_id, $advance->amount);
            $wallet = $this->updateUserWallet($advance->from_user_id, $advance->amount);
            $advance->current_balance = $wallet->amount;
            $advance->save();

            NewRevenueAddedEvent::dispatch($advance->amount);
            $this->createNotificationToClientAfterAdvancePaid($advance);
        }
    }
    private function handleStatusChange($advance, $oldStatus, $newStatus, $diff, $userType): void
    {
        $isEmployee = $userType == UserType::Broker->value;
        $userId = $isEmployee ? $advance->to_user_id : $advance->from_user_id;
        $walletChange = 0;

        if (Status::Pending->equals($oldStatus) && Status::Approved->equals($newStatus)) {
            $walletChange = $advance->amount;
        } elseif (Status::Rejected->equals($oldStatus) && Status::Approved->equals($newStatus)) {
            $walletChange = $advance->amount;
        } elseif (Status::Approved->equals($oldStatus) && Status::Pending->equals($newStatus)) {
            $walletChange = -$advance->amount;
        } elseif (Status::Approved->equals($oldStatus) &&  Status::Rejected->equals($newStatus)) {
            $walletChange = -$advance->amount;
        } elseif (Status::Approved->equals($oldStatus) &&  Status::Approved->equals($newStatus)) {
            $walletChange = $diff;
        }

        if ($isEmployee) {
            $this->updateWorkerDue($userId, $walletChange);
            $wallet = $this->updateUserWallet($userId, $walletChange, !$isEmployee);
            NewExpenseAddedEvent::dispatch($walletChange);
        } else {
            $this->updateClientDebit($userId, $walletChange);
            $wallet = $this->updateUserWallet($userId, $walletChange);
            NewRevenueAddedEvent::dispatch($walletChange);
        }
        if ($walletChange != 0) {
            $advance->current_balance = $wallet->amount;
            $advance->save();
        }
        $this->createNotificationToUserAfterAdvanceChange($advance, $userId);
    }
    private function notifyAdminOnClientAction($advance, $action = 'create'): void
    {
        $advance->load('fromUser');
        $admins = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $actionText = $action == 'create' ? 'Has Added An Advance' : 'Has Updated Advance';

        $params = [
            'title' => __("The Client") . ' ' . $advance->fromUser->name . ' ' . __($actionText),
            'content' => __("The Client") . ' ' . $advance->fromUser->name . ' ' . __($actionText) . ' ' . __("of total ") . $advance->amount,
            'to' => $admins,
            'from_user_id' => auth('web')->id(),
            'route' => 'admin.advances.edit',
            'routeParams' => $advance->id,
            'reference_id' => $advance->id,
            'reference_type' => get_class($advance),
        ];

        (new NotificationsService())->createNotification($params);
    }

    public function updateClientDebit($user_id, $amount)
    {
        $client_debit = ClientDebit::where("user_id", $user_id)->first();
        $revenue_amount = $amount;
        if (isset($client_debit)) {
            if ($amount > $client_debit->amount && $client_debit->amount > 0) {
                $revenue_amount = $client_debit->amount;
            }
            ClientDebit::where("user_id", $user_id)->decrement("amount", $amount);
        } else {
            ClientDebit::create([
                "user_id" => $user_id,
                "amount" => -$amount
            ]);
        }
        return $revenue_amount;
    }
    public function updateWorkerDue($user_id, $amount)
    {
        $worker_due = WorkerDue::where("user_id", $user_id)->first();
        $expense_amount = $amount;
        if (isset($worker_due)) {
            if ($amount > $worker_due->amount && $worker_due->amount > 0) {
                $expense_amount = $worker_due->amount;
            }
            WorkerDue::where("user_id", $user_id)->decrement("amount", $amount);
        } else {
            WorkerDue::create([
                "user_id" => $user_id,
                "amount" => -$amount
            ]);
        }
        return $expense_amount;
    }

    public function updateUserWallet($user_id, $amount, $increase = true)
    {
        if (!$increase) {
            $amount = -$amount;
        }
        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet = Wallet::create([
                "user_id" => $user_id,
                "amount" => $amount
            ]);
        } else {
            $wallet->amount += $amount;
            $wallet->save();
        }
        return $wallet;
    }

    public function createNotificationToWorkerAfterAdvancePaid($advance)
    {
        $params = [
            "title" => __("The Admin Has Gaven an Advance To you"),
            "content" => __("The Admin Has Gaven an Advance To you of total") . " " . $advance->amount,
            "to" => [
                $advance->to_user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToClientAfterAdvancePaid($advance)
    {
        $params = [
            "title" => __("The Admin Has Added an Advance From you"),
            "content" => __("The Admin Has Added an Advance From you of total") . " " . $advance->amount,
            "to" => [
                $advance->from_user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToUserAfterAdvanceChange($advance, $user_id)
    {
        $params = [
            "title" => __("The Admin Has Made Changes To Your Advance"),
            "content" => __("The Admin Has Made Changes To Your Advance of total") . " " . $advance->amount . " " . __("Its Status is ") . __($advance->status->label()),
            "to" => [
                $user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }

    private function handleApprovedAtDate($advance, $status)
    {
        if (Status::Approved->equals($status) && !$advance->approved_at) {
            $advance->approved_at = now();
        } else if (!Status::Approved->equals($status) && $advance->approved_at) {
            $advance->approved_at = null;
        }
        $advance->save();
    }
}
