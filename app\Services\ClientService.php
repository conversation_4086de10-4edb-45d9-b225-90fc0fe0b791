<?php

namespace App\Services;

use App\Constants\UserType;
use App\Models\Client;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class ClientService
{
    public function create(array $data)
    {
        try {
            DB::beginTransaction();
            $client = Client::create([
                "name_ar" => $data["name_ar"],
                "name_en" => $data["name_en"],
                "phone" => $data["phone"],
                "address" => $data["address"],
                "company_name" => $data["company_name"],
            ]);

            if (isset($data["can_login"])) {
                $user = User::create([
                    "username" => $data["username"],
                    "name_ar" => $data["name_ar"],
                    "name_en" => $data["name_en"],
                    "phone" => $data["phone"],
                    "address" => $data["address"],
                    "email" => @$data["email"] ?? $data["phone"],
                    "password" => bcrypt($data["password"]),
                    "status" => "Active",
                    "type" => UserType::Client,
                    "IsSuperAdmin" => 0
                ]);
                $client->user_id = $user->id;
                $client->save();
                $this->assignRoleToClient($user->id);
            }

            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }
    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $client = Client::findOrFail($id);
            $client->update([
                "name_ar" => $data["name_ar"],
                "name_en" => $data["name_en"],
                "phone" => $data["phone"],
                "address" => $data["address"],
                "company_name" => $data["company_name"],
                "IsSuperAdmin" => 0
            ]);

            if (isset($data["can_login"])) {
                if (!isset($client->user_id)) {
                    $user = User::create([
                        "name_ar" => $data["name_ar"],
                        "name_en" => $data["name_en"],
                        "username" => $data["username"],
                        "phone" => $data["phone"],
                        "address" => $data["address"],
                        "email" => @$data["email"] ?? $data["phone"],
                        "password" => bcrypt($data["password"]),
                        "status" => "Active",
                        "type" => UserType::Client
                    ]);
                    $client->user_id = $user->id;
                    $client->save();
                } else {
                    $user = User::findOrFail($client->user_id);
                    $user->update([
                        "name_ar" => $data["name_ar"],
                        "name_en" => $data["name_en"],
                        "username" => $data["username"],
                        "phone" => $data["phone"],
                        "address" => $data["address"],
                        "email" => @$data["email"] ?? $data["phone"],
                    ]);
                    if (isset($data["password"])) {
                        $user->password =  bcrypt($data["password"]);
                        $user->save();
                    }
                }
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function assignRoleToClient($user_id)
    {
        try {
            $role = Role::findByName("Client");
            $user = User::findOrFail($user_id);
            if (isset($role)) {

                $user->syncRoles([$role->name]);
            } else {
                $clientRole = Role::firstOrCreate([
                    "name" => "Client",
                    "guard_name" => "web"
                ]);
                $clientRole->syncPermissions([
                    "View_Related_Custom_Shippings",
                    "View_Related_Worker_Dues",
                    "View_Related_Advances",
                    "View_Shipping_Invoices",
                    "View_Related_Client_Debits",
                    // "View_Related_Invoices"
                ]);
                $user->syncRoles([$clientRole->name]);
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
