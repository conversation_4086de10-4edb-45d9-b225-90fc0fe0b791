<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class HsSheet extends Model
{
    protected $fillable = [
        "section_code",
        "section_name_ar",
        "section_name_en",
        "chapter_code",
        "chapter_name_ar",
        "chapter_name_en",
        "heading_code",
        "heading_name_ar",
        "heading_name_en",
        "hs_code",
        "duty",
        "name_en",
        "name_ar",
        "description",
        "unit",
        "restriction_import",
        "restriction_export",
        "prohibition_import",
        "prohibition_export",
    ];

    protected $casts = [
        "restriction_import" => "json",
        "prohibition_import" => "json",
        "restriction_export" => "json",
        "prohibition_export" => "json",
    ];
}
