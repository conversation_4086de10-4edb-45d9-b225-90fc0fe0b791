<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale =  session()->get("locale");
        if (isset($locale)) {
            app()->setLocale($locale);
        } else {
            session()->put("locale", "ar");
            app()->setLocale("ar");
        }
        return $next($request);
    }
}
