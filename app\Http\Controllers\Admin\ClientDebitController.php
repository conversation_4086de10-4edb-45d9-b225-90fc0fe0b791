<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\Client;
use App\Models\Wallet;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Http\Controllers\Controller;
use App\Services\ClientDebitService;
use App\Http\Requests\Admin\Advance\UpdateClientDebitRequest;

class ClientDebitController extends Controller
{
    protected $clientDebitService;
    public function __construct()
    {
        $this->clientDebitService = new ClientDebitService();
        $this->middleware('is_able:Create Client Debit')->only(['create', 'store']);
        // $this->middleware('is_able:View_Client_Debits')->only(['index', 'view']);
        // $this->middleware('is_able:Update_Client_Debit')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Client Debit')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (is_admin()) {
            $data["title"] = __("Clients Debits");
            $data["sub_title"] = __("Clients Debits");
        } else {
            $data["title"] = __("Outstanding Balance");
            $data["sub_title"] = __("Outstanding Balance");
        }
        $user = User::find(auth("web")->id());
        $debit = ClientDebit::with("user.client")
            ->when($user, function ($query) use ($user) {
                if ($user->type == UserType::Client && $user->can('View Related Client Debits')) {
                    $query->where("user_id", $user->id);
                } else if ($user->type == UserType::SubClient && $user->can('View Related Client Debits')) {
                    $client_id =  get_client_id();
                    $client = Client::findOrFail($client_id);
                    $query->where("user_id", $client->user_id);
                }
            })->first();
        $data["client_debit"] = $debit;
        $data["wallet_balance"] = Wallet::where("user_id", $user->id)->value("amount");
        return view('admin.client_debits.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $user = User::find(auth("web")->id());
            $query = ClientDebit::with(["user.client", "user.wallet"])
                ->when($user, function ($query) use ($user) {
                    if ($user->type == UserType::Client && $user->can('View Related Client Debits')) {
                        $query->where("user_id", $user->id);
                    } else if ($user->type == UserType::SubClient && $user->can('View Related Client Debits')) {
                        $client_id =  get_client_id();
                        $client = Client::findOrFail($client_id);
                        $query->where("user_id", $client->user_id);
                    }
                })
                ->when($search, function ($query) use ($search) {
                    $query->whereHas("user", function ($q) use ($search) {
                        $q->where("name", "LIKE", "%" . $search . "%")
                            ->orWhere("email", "LIKE", "%" . $search . "%");
                    });
                });
            $TotalCount = $query->count();

            if ($orderBy == "user_name") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'client_debits.user_id')
                    ->orderBy('u.name', $direction)->select("client_debits.*");
            } else if ($orderBy == "wallet_balance") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'client_debits.user_id')
                    ->leftJoin('wallets as w', 'w.user_id', '=', 'u.id')
                    ->orderBy("w.amount", $direction)->select("client_debits.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $users = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.client_debits.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateClientDebitRequest $request, $id)
    {
        try {
            $this->clientDebitService->updateClientDebit($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $e) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return redirect()->back();
        }
    }
}
