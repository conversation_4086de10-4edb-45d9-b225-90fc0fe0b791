<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\Client;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\ClientService;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Client\CreateRequest;
use App\Http\Requests\Admin\Client\UpdateRequest;
use App\Models\Company;
use App\Models\User;
use App\Models\Setting;
use App\Services\PdfService;

class ClientController extends Controller
{
    protected $clientService;
    public function __construct()
    {
        $this->clientService = new ClientService();
        $this->middleware('is_able:Create Client')->only(['create', 'store']);
        $this->middleware('is_able:View Clients')->only(['index', 'view']);
        $this->middleware('is_able:Update Client')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Client')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Clients");
        $data["sub_title"] = __("Clients List");
        return view('admin.clients.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = Client::with("companies")->when($search, function ($query) use ($search) {
                $query->where("name_ar", "LIKE", "%" . $search . "%")
                    ->orWhere("name_en", "LIKE", "%" . $search . "%")
                    ->orWhere("phone", "LIKE", "%" . $search . "%")
                    ->orWhere("company_name", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            $clients = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $clients;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.clients.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Clients");
        $data["sub_title"] = __("Add New Client");
        return view('admin.clients.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->clientService->create($request->validated());
            session()->flash("success", CrudMessage::CreatedSuccessfully);
            return redirect()->back();
        } catch (Exception $e) {
            return $e;
            session()->flash("error", CrudMessage::SomethingWentWrong);
            return redirect()->back();
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('admin.show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $data["title"] = __("Clients");
            $data["sub_title"] = __("Update Client");

            $data["client"] = Client::findOrFail($id);

            return view('admin.clients.edit', $data);
        } catch (Exception $e) {
            session()->flash("error", $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->clientService->update($request->validated(), $id);
            session()->flash("success", CrudMessage::UpdatedSuccessfully);
            return redirect()->back();
        } catch (Exception $e) {
            return $e;
            session()->flash("error", CrudMessage::SomethingWentWrong);
            return redirect()->back();
        }
    }

    public function getClientCompanies($id)
    {
        try {
            $items = Company::where('client_id', $id)->get();
            return response()->json(['success' => true, "data" => $items]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, "error" => $th->getMessage()]);
        }
    }
    public function getClientSubClients($user_id)
    {
        try {
            $client = Client::where('user_id', $user_id)->first();
            $items = User::where('client_id', $client->id)
                ->orWhere("id", $user_id)
                ->get();
            $invoice_number = getClientNextInvoiceNumber($user_id);
            return response()->json(['success' => true, "data" => $items, "invoice_number" => $invoice_number]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, "error" => $th->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            Client::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = Setting::where('key', 'logo')->first();
        $clients = Client::query()->get();

        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.clients.export_pdf', [
            'clients' => $clients,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_clients.pdf", 'D');
    }
}
