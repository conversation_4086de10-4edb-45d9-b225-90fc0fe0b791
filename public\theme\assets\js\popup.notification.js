function alertDefault(msg) {
    Lobibox.notify("default", {
        pauseDelayOnHover: true,
        size: "mini",
        rounded: true,
        delayIndicator: false,
        continueDelayOnInactiveTab: false,
        position: "top right",
        msg,
    });
}

function alertInfo(msg) {
    Lobibox.notify("info", {
        pauseDelayOnHover: true,
        size: "mini",
        rounded: true,
        icon: "fas fa-info-circle",
        delayIndicator: false,
        continueDelayOnInactiveTab: false,
        position: "top right",
        msg,
    });
}

function alertWarning(msg) {
    Lobibox.notify("warning", {
        pauseDelayOnHover: true,
        size: "mini",
        rounded: true,
        delayIndicator: false,
        icon: "fas fa-exclamation-triangle",
        continueDelayOnInactiveTab: false,
        position: "top right",
        msg,
    });
}

function alertError(msg) {
    Lobibox.notify("error", {
        pauseDelayOnHover: true,
        size: "mini",
        rounded: true,
        delayIndicator: false,
        icon: "fas fa-times-circle",
        continueDelayOnInactiveTab: false,
        position: "top right",
        msg,
    });
}

function alertSuccess(msg) {
    Lobibox.notify("success", {
        pauseDelayOnHover: true,
        size: "mini",
        rounded: true,
        icon: "fas fa-check",
        delayIndicator: false,
        continueDelayOnInactiveTab: false,
        position: "top right",
        msg,
    });
}
