<?php

namespace App\Http\Requests\Admin\Purchase;

use App\Constants\CrudMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "finance_category_id" => "required|integer",
            "date" => "nullable|date",
            "amount" => "required",
            "description" => "nullable",
            "updated_by" => "sometimes",
            "attachments" => "nullable|array",
            "attachments.*" => "nullable|file|mimes:pdf,doc,png,jpeg,xlsx,csv",
        ];
    }


    public function messages(): array
    {
        return [
            "amount.required" => CrudMessage::isRequired(__("Amount")),
            "finance_category_id.required" => CrudMessage::isRequired(__("Type")),
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'updated_by' => auth("web")->id(),
        ]);
    }
}
