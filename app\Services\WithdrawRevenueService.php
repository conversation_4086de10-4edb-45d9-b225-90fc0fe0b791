<?php

namespace App\Services;

use Illuminate\Support\Str;
use App\Models\FinancialMonth;
use App\Models\WithdrawRevenue;
use Illuminate\Support\Facades\DB;
use App\Constants\FinancialMonthStatus;
use App\Constants\AttachmentVisibleStatus;

class WithdrawRevenueService
{
    public function store(array $data)
    {
        try {
            DB::beginTransaction();
            $withdraw = WithdrawRevenue::create($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/withdraws/$withdraw->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $withdraw->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }

            $current_month = FinancialMonth::where('status', FinancialMonthStatus::Opened)->first();
            $current_month->withdrawn_revenues += $withdraw->amount;
            $current_month->save();
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $withdraw = WithdrawRevenue::findOrFail($id);
            $new_amount = $data["amount"];
            $amount_diff = $new_amount -  $withdraw->amount;
            $withdraw->update($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/withdraws/$withdraw->id";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $withdraw->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            $current_month = FinancialMonth::where('status', FinancialMonthStatus::Opened)->first();
            $current_month->withdrawn_revenues += $amount_diff;
            $current_month->save();
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
