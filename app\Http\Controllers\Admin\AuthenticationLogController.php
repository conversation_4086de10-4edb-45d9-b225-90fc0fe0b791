<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\AuthenticationLog;
use App\Http\Controllers\Controller;

class AuthenticationLogController extends Controller
{
    public function index()
    {
        $data["title"] = __("Login History");
        $data["sub_title"] = __("Login History");
        return view('admin.logs.authentication_logs.index', $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = $request->RowsPerPage;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(string: Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = AuthenticationLog::with("user")->when($search, function ($query) use ($search) {
                $query->whereHas("user", function ($q) use ($search) {
                    $q->where("name", "LIKE", "%" . $search . "%")
                        ->orWhere("email", "LIKE", "%" . $search . "%");
                })->orWhere("ip_address", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            if ($orderBy == "user_name") {
                $query = $query
                    ->join('users as u', 'u.id', '=', 'authentication_logs.user_id')
                    ->orderBy('u.name', $direction)->select("authentication_logs.*");
            } else {
                $query = $query->orderBy($orderBy, $direction);
            }

            $users = $query->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $users;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = min($skip + $RowsPerPage, $TotalCount);
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.logs.authentication_logs.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            return $e;
            $view = view("components.something_went_wrong")->render();
            return response($view);
        }
    }
}
