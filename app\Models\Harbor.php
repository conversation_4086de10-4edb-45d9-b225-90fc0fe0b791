<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Harbor extends Model
{
    protected $fillable = [
        "name",
        "name_ar",
        "name_en",
        "code",
        "country_id",
        "location"
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function Name(): Attribute
    {
        return Attribute::make(get: function () {
            if (app()->getLocale() == "ar") {
                return $this->name_ar;
            } elseif (app()->getLocale() == "en") {
                return $this->name_en;
            } else {
                return $this->name;
            }
        });
    }
}
