<?php

namespace App\Exports;

use App\Models\CustomShipping;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomShippingExport implements FromCollection, WithHeadings, WithStyles
{
    public function collection()
    {
        $data = [];
        $shippings = CustomShipping::query()->with("assignedTo")->get();

        foreach ($shippings as $shipping) {
            $data[] = [
                "Policy Number" => $shipping->policy_number,
                "Type" => $shipping->type_str,
                "Packages Count" => $shipping->packages_count,
                "Units Count" => $shipping->units_count,
                "Total Weight" => $shipping->total_weight,
                "Total Size" => $shipping->total_size,
                "Assigned To" => $shipping->assignedTo->name,
                "Status" => $shipping->status_str,
                "Created At" => $shipping->created_at->toDateString(),
            ];
        }

        return new Collection($data);
    }

    public function headings(): array
    {
        return [
            __("Policy Number"),
            __("Type"),
            __("Packages Count"),
            __("Units Count"),
            __("Total Weight"),
            __("Total Size"),
            __('Assigned To'),
            __('Status'),
            __('Created At')
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:G1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Auto-size columns
        foreach (range('A', 'G') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Style data cells
        $sheet->getStyle('A2:G101')->applyFromArray([
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
        ]);

        return $sheet;
    }
}
