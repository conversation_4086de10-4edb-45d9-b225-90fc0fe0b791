<?php

namespace App\Http\Requests\Admin\User;

use App\Constants\CrudMessage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "username" => "required|unique:users,username," . auth("web")->id(),
            "name_ar" => "required|max:100",
            "name_en" => "required|max:100",
            "email" => "required|email|unique:users,email," . auth("web")->id(),
            "phone" => "required",
            "address" => "sometimes",
            "avatar" => "sometimes|file|mimes:png,jpg,jpeg|max:5000"
        ];
    }


    public function messages(): array
    {
        return [
            "name_ar.required" => CrudMessage::isRequired(__("Arabic Name")),
            "name_ar.max" => CrudMessage::maxLengthCharacters(100),
            "name_en.required" => CrudMessage::isRequired(__("English Name")),
            "name_en.max" => CrudMessage::maxLengthCharacters(100),
            "email.required" => CrudMessage::isRequired("email"),
            "phone.required" => CrudMessage::isRequired("phone"),
            "email.unique" => CrudMessage::alreadyExists("email"),
            "email.email" => CrudMessage::isNotValid("email"),
            "username.unique" => CrudMessage::alreadyExists("Username"),
            "username.required" => CrudMessage::isRequired("Username"),
        ];
    }
}
