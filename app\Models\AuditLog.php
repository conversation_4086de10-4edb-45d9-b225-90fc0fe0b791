<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    protected $fillable = [
        "user_id",
        "model_type",
        "action",
        "model_id",
        "changes",
        "ip_address",
        "route_name"
    ];

    protected $casts = [
        "changes" => "json"
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
