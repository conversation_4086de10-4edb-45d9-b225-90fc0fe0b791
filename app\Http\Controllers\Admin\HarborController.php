<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\Harbor;
use App\Models\Country;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HarborController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Harbors");
        $data["countries"] = Country::all();
        return view("admin.intialization.harbors.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Harbor::when($search, function ($query) use ($search) {
                $query
                    ->where("name", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.intialization.harbors.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => "required",
            "country_id" => "required"
        ]);
        try {
            $data = $request->only(['name', "country_id"]);
            $harbor = Harbor::create($data);
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $harbor = Harbor::findOrFail($id);
            return response()->json(['success' => true, 'data' => $harbor]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "name" => "required",
            "country_id" => "required"
        ]);
        try {
            $data = $request->only(['name', "country_id"]);
            $harbor = Harbor::findOrFail($id);
            $harbor->update($data);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            Harbor::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
}
