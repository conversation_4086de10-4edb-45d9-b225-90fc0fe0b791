<?php

namespace App\Services;

use Exception;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WorkerDue;
use App\Constants\UserType;
use App\Models\ClientDebit;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Models\CustomShipping;
use App\Models\ShippingExpense;
use App\Models\ShippingInvoice;
use Illuminate\Support\Facades\DB;
use App\Constants\ShippingInvoiceStatus;
use App\Constants\AttachmentVisibleStatus;

class __ShippingInvoiceService
{
    public function storeClientInvoice(array $data)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::create($data);
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/custom_shippings/$invoice->custom_shipping_id/invoices";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $invoice->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            foreach ($data["shipping_expense"] as $shipping_expense) {
                $expense_id = @$shipping_expense["id"];
                if (isset($expense_id)) {
                    $expense = ShippingExpense::findOrFail($expense_id);
                    $expense->shipping_expense_type_id = $shipping_expense["shipping_expense_type_id"];
                    $expense->amount = $shipping_expense["amount"];
                    $expense->notes = $shipping_expense["notes"];
                    $expense->save();
                } else {
                    ShippingExpense::updateOrCreate([
                        "shipping_invoice_id" => $invoice->id,
                        "shipping_expense_type_id" => $shipping_expense["shipping_expense_type_id"],
                    ], [
                        "amount" => $shipping_expense["amount"],
                        "notes" => $shipping_expense["notes"],
                    ]);
                }
            }
            $this->createClientDebit($invoice->id, $invoice->user_id);
            $this->updateUserWallet($invoice->id, $invoice->user_id);
            $this->createNotificationToClientAfterApproval($invoice);
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }
    /**
     *  update a shipping invoice
     * create or update shipping expenses
     * upload attachments
     * change status of invoice and add balance to worker
     * @param array $data
     * @return void
     */
    public function createOrUpdateInvoice(array $data)
    {
        try {
            $status_changed = false;
            DB::beginTransaction();
            $invoice = ShippingInvoice::with("user")->where("id", $data["invoice_id"])->first();
            //update invoice status 
            $old_status = $invoice->status->value;
            $new_status =  $data["status"];
            //TODO
            if (isset($data["status"]) && $invoice->status->value != $data["status"]) {
                $status_changed = true;
                $invoice->status = $data["status"];
                $invoice->updated_by = auth("web")->id();
                $invoice->notes =  @$data["notes"];
                $invoice->save();
            } else {
                $invoice->updated_by = auth("web")->id();
                $invoice->notes =  @$data["notes"];
                $invoice->save();
            }
            if (isset($data["attachments"])) {
                foreach ($data["attachments"] as $attachment) {
                    $original_fielname = $attachment->getClientOriginalName();
                    $mime_type = $attachment->getClientOriginalExtension();
                    $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                    $path         = "uploads/custom_shippings/$invoice->custom_shipping_id/invoices";
                    $uploadPath   = public_path($path);
                    $attachment->move($uploadPath, $uniqueName);
                    $file_path = $path . "/" . $uniqueName;

                    $invoice->attachments()->create([
                        "file_path" => $file_path,
                        "filename" => $uniqueName,
                        "original_filename" => $original_fielname,
                        "mime_type" => $mime_type,
                        "visible_to" => AttachmentVisibleStatus::Employee
                    ]);
                }
            }
            $total_amount = 0;
            $invoice_ = ShippingInvoice::findOrFail($invoice->id);
            $invoice_ = $invoice->load("shippingExpenses");
            $total_amount_before = $invoice_->total_amount;
            foreach ($data["shipping_expense"] as $shipping_expense) {
                $expense_id = @$shipping_expense["id"];
                $total_amount += $shipping_expense["amount"];
                if (isset($expense_id)) {
                    $expense = ShippingExpense::findOrFail($expense_id);
                    $expense->shipping_expense_type_id = $shipping_expense["shipping_expense_type_id"];
                    $expense->amount = $shipping_expense["amount"];
                    $expense->notes = $shipping_expense["notes"];
                    $expense->save();
                } else {
                    ShippingExpense::updateOrCreate([
                        "shipping_invoice_id" => $invoice->id,
                        "shipping_expense_type_id" => $shipping_expense["shipping_expense_type_id"],
                    ], [
                        "amount" => $shipping_expense["amount"],
                        "notes" => $shipping_expense["notes"],
                    ]);
                }
            }
            //From Pending/Rejected ==> Approved
            if ($status_changed && $data["status"] == ShippingInvoiceStatus::Approved->value) {
                //TODO 
                //check if employee or client
                //create worker due 
                if ($invoice->user->type == UserType::Employee) {
                    $this->createWorkerDue($invoice->id, $invoice->user_id);
                    $this->updateUserWallet($invoice->id, $invoice->user_id);
                    //send Notification To Worker
                    $this->createNotificationToWorkerAfterApproval($invoice);
                } elseif ($invoice->user->type == UserType::Client) {
                    $this->createClientDebit($invoice->id, $invoice->user_id);
                    $this->updateUserWallet($invoice->id, $invoice->user_id);
                    //send Notification To Client
                    $this->createNotificationToClientAfterApproval($invoice);
                }
            }
            //From Approved ==> Pending/Rejected
            if (ShippingInvoiceStatus::Approved->equals($old_status) && (ShippingInvoiceStatus::Pending->equals($new_status) || ShippingInvoiceStatus::Rejected->equals($new_status))) {
                if ($invoice->user->type == UserType::Employee) {
                    $this->decreaseWorkerDue($invoice->id, $invoice->user_id);
                    $this->updateUserWallet(invoice_id: $invoice->id, user_id: $invoice->user_id, cut: false);
                } elseif ($invoice->user->type == UserType::Client) {
                    $this->decreaseClientDebit($invoice->id, $invoice->user_id);
                    $this->updateUserWallet(invoice_id: $invoice->id, user_id: $invoice->user_id, cut: false);
                }
            }
            //From Approved ==> Approved
            if (ShippingInvoiceStatus::Approved->equals($old_status) && ShippingInvoiceStatus::Approved->equals($new_status)) {
                // $invoice_ = ShippingInvoice::findOrFail($invoice->id);
                // $invoice_ = $invoice->load("shippingExpenses");
                // $total_amount_before = $invoice_->total_amount;
                $total_amount_after = $total_amount - $total_amount_before;
                // dd($total_amount_after);
                if ($invoice->user->type == UserType::Employee) {
                    $this->decreaseWorkerDue($invoice->id, $invoice->user_id, $total_amount_after);
                    $this->decreaseUserWallet(invoice_id: $invoice->id, user_id: $invoice->user_id, amount: $total_amount_after);
                } elseif ($invoice->user->type == UserType::Client) {
                    $this->decreaseClientDebit($invoice->id, $invoice->user_id, $total_amount_after);
                    $this->decreaseUserWallet(invoice_id: $invoice->id, user_id: $invoice->user_id, amount: $total_amount_after);
                }
            }

            if (is_employee() && !is_admin()) {
                $this->createNoificationForAdminAfterInvoiceChange($invoice);
            }
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function createWorkerDue($invoice_id, $user_id)
    {
        $invoice = ShippingInvoice::findOrFail($invoice_id);
        $invoice = $invoice->load("shippingExpenses");
        $total_amount = $invoice->total_amount;
        $workerDue = WorkerDue::where(['user_id' => $user_id])->first();
        if (!isset($workerDue)) {
            $workerDue = WorkerDue::create([
                "user_id" => $user_id,
                "amount" => $total_amount,
                "created_by" => auth("web")->id()
            ]);
        } else {
            $workerDue->amount += $total_amount;
            $workerDue->updated_by = auth("web")->id();
            $workerDue->save();
        }
    }
    public function decreaseWorkerDue($invoice_id, $user_id, $amount = null)
    {
        $invoice = ShippingInvoice::findOrFail($invoice_id);
        $invoice = $invoice->load("shippingExpenses");
        $total_amount = $amount ?? $invoice->total_amount;
        $workerDue = WorkerDue::where(['user_id' => $user_id])->first();
        $workerDue->amount -= $total_amount;
        $workerDue->updated_by = auth("web")->id();
        $workerDue->save();
    }
    public function createClientDebit($invoice_id, $user_id)
    {
        $invoice = ShippingInvoice::findOrFail($invoice_id);
        $invoice = $invoice->load("shippingExpenses");
        $total_amount = $invoice->total_amount;
        $clientDebit = ClientDebit::where(['user_id' => $user_id])->first();
        if (!isset($clientDebit)) {
            $clientDebit = ClientDebit::create([
                "user_id" => $user_id,
                "amount" => $total_amount,
                "created_by" => auth("web")->id()
            ]);
        } else {
            $clientDebit->amount += $total_amount;
            $clientDebit->updated_by = auth("web")->id();
            $clientDebit->save();
        }
    }

    // if you cut from the debit or due then you increase the wallet
    public function updateUserWallet($invoice_id, $user_id, $amount = null, $cut = true)
    {
        if (!isset($amount)) {
            $invoice = ShippingInvoice::findOrFail($invoice_id);
            $invoice = $invoice->load("shippingExpenses");
            $amount = $invoice->total_amount;
        }
        if (!$cut) {
            $amount = -$amount;
        }
        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet = Wallet::create([
                "user_id" => $user_id,
                "amount" => -$amount
            ]);
        } else {
            $wallet->amount -= $amount;
            $wallet->save();
        }

        $invoice->current_balance = $wallet->amount;
        $invoice->save();
    }
    public function decreaseUserWallet($invoice_id, $user_id, $amount = null)
    {
        $invoice = ShippingInvoice::findOrFail($invoice_id);
        $invoice = $invoice->load("shippingExpenses");
        if (!isset($amount)) {
            $amount = $invoice->total_amount;
        }
        $amount = -$amount;
        $wallet = Wallet::where("user_id", "=", $user_id)->first();
        if (!isset($wallet)) {
            $wallet =  Wallet::create([
                "user_id" => $user_id,
                "amount" => $amount
            ]);
        } else {
            $wallet->amount += $amount;
            $wallet->save();
        }
        $invoice->current_balance = $wallet->amount;
        $invoice->save();
    }
    public function decreaseClientDebit($invoice_id, $user_id, $amount = null)
    {
        $invoice = ShippingInvoice::findOrFail($invoice_id);
        $invoice = $invoice->load("shippingExpenses");
        $total_amount = $amount ?? $invoice->total_amount;
        $clientDebit = ClientDebit::where(['user_id' => $user_id])->first();
        $clientDebit->amount -= $total_amount;
        $clientDebit->updated_by = auth("web")->id();
        $clientDebit->save();
    }

    public function createClientInvoice($invoice_id)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::findOrFail($invoice_id);
            $custom_shipping = CustomShipping::findOrFail($invoice->custom_shipping_id);
            $custom_shipping = $custom_shipping->load("client");
            $invoice = $invoice->load("shippingExpenses", "attachments");

            $invoice_attributes = $invoice->toArray();

            $newAttributes = Arr::except($invoice_attributes, ['id', 'created_at', 'updated_at']);
            $newAttributes["user_id"] = $custom_shipping->client->user_id;
            $newAttributes["status"] = ShippingInvoiceStatus::Pending;
            $wallet = Wallet::where("user_id", $custom_shipping->client->user_id)->first();
            $newAttributes["current_balance"] = $wallet->amount;
            $new_invoice = ShippingInvoice::create($newAttributes);
            foreach ($invoice->shippingExpenses as $expense) {
                $new_invoice->shippingExpenses()->create([
                    "shipping_expense_type_id" => $expense->shipping_expense_type_id,
                    "amount" => $expense->amount
                ]);
            }
            foreach ($invoice->attachments as $attachment) {
                $new_invoice->attachments()->create([
                    "file_path" => $attachment->file_path,
                    "filename" => $attachment->filename,
                    "original_filename" => $attachment->original_filename,
                    "mime_type" => $attachment->mime_type,
                    "visible_to" => AttachmentVisibleStatus::Client
                ]);
            }
            DB::commit();
            return $new_invoice->id;
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function createNotificationToWorkerAfterApproval($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $params = [
            "title" => __("The Admin Has Approved The Invoice"),
            "content" => __("The Admin Has Approved The Invoice Of Code") . " " . $shippingInvoice->invoice_code,
            "to" => [
                $shippingInvoice->user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.edit",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function createNotificationToClientAfterApproval($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $params = [
            "title" => __("The Admin Has Created An Invoice For You"),
            "content" => __("The Admin Has Created An Invoice Of Code") . " " . $shippingInvoice->invoice_code . " " . __("For The Shipping Of Plicy Number") . " " . $shippingInvoice->customShipping->policy_number,
            "to" => [
                $shippingInvoice->user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.exportPdf",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }

    public function createNoificationForAdminAfterInvoiceChange($shippingInvoice)
    {
        $shippingInvoice = $shippingInvoice->load("customShipping");
        $to = User::where("IsSuperAdmin", 1)->pluck("id")->toArray();
        $params = [
            "title" => __("The Broker Has Made Changes To The Invoice"),
            "content" => __("The Broker Has Made Changes To The Invoice Of Code") . " " . $shippingInvoice->invoice_code,
            "to" => $to,
            "from_user_id" => auth("web")->id(),
            "route" => "admin.custom_shipping_invoices.edit",
            "routeParams" => $shippingInvoice->id,
            "reference_id" => $shippingInvoice->id,
            "reference_type" => get_class($shippingInvoice)
        ];
        (new NotificationsService())->createNotification($params);
    }
    public function deleteInvoice($id)
    {
        try {
            DB::beginTransaction();
            $invoice = ShippingInvoice::with("attachments", "shippingExpenses")->findOrFail($id);
            $total_amount = $invoice->total_amount;
            foreach ($invoice->attachments as $attachment) {
                $attachment->delete();
            }
            foreach ($invoice->shippingExpenses as $expense) {
                $expense->delete();
            }
            $clientDebit = ClientDebit::where(['user_id' => $invoice->user_id])->first();
            if (isset($clientDebit)) {
                $clientDebit->amount -= $total_amount;
                $clientDebit->updated_by = auth("web")->id();
                $clientDebit->save();
            }
            $invoice->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}


// if (!isset($invoice)) {
//     $invoice = ShippingInvoice::create([
//         "invoice_number" => generateUniqueRandomNumberStringToTable("shipping_invoices", "invoice_number", 6),
//         "custom_shipping_id" => $data["custom_shipping_id"],
//         "user_id" => auth("web")->id(),
//         "status" => 1,
//         "created_by" => auth("web")->id(),
//         "notes" => @$data["notes"]
//     ]);
// } else {
//     //update
// }