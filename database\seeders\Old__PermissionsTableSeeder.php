<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class Old__PermissionsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            "Create_User",
            "View_Users",
            "Delete_User",
            "Update_User",

            "Create_Role",
            "View_Roles",
            "Delete_Role",
            "Update_Role",

            "View_Hs_Sheet",
            "View_General_Settings",
            "Update_General_Settings",

            "View_Intialization_Settings",
            "Update_Intialization_Settings",

            "View_All_Custom_Shippings",
            "View_Related_Custom_Shippings", //related to broker
            "View Created By Custom Shippings",
            "Assign Custom Shipping To Broker",
            "Assign Custom Shipping To Client",
            "Upload Attachment In Custom Shipping",
            "Create_Custom_Shipping",
            "Change_Custom_Shipping_Status",
            "Update_Custom_Shipping",
            "Delete_Custom_Shipping",

            "Change_Shipping_Invoice_Status",
            "View_Shipping_Invoices",
            "Update_Shipping_Invoice",
            "Create_Shipping_Invoice",
            "Delete_Shipping_Invoice",

            "Create_Worker_Due",
            "View_Worker_Dues",
            "View_Related_Worker_Dues",
            "Delete_Worker_Due",
            "Update_Worker_Due",

            "Create_Advance",
            "View_Advances",
            "View_Related_Advances",
            "Delete_Advance",
            "Update_Advance",

            "Create_Client",
            "View_Clients",
            "Delete_Client",
            "Update_Client",

            "Create_Company",
            "View_Companies",
            "Delete_Company",
            "Update_Company",

            "Create_Client_Debit",
            "View_Client_Debits",
            "View_Related_Client_Debits",
            "Delete_Client_Debit",
            "Update_Client_Debit",

            "Create_Shipping_Expense_Type",
            "View_Shipping_Expense_Types",
            "Delete_Shipping_Expense_Type",
            "Update_Shipping_Expense_Type",

            "Create_Document",
            "View_Documents",
            "Delete_Document",
            "Update_Document",

            "Create_Expense",
            "View_Expenses",
            "Delete_Expense",
            "Update_Expense",

            "Create_Purchase",
            "View_Purchases",
            "Delete_Purchase",
            "Update_Purchase",

            "Create_Revenue",
            "View_Revenues",
            "Delete_Revenue",
            "Update_Revenue",
        ];

        foreach ($permissions as $permission) {
            Permission::findOrCreate($permission);
        }

        $shipping_supervisor = Role::firstOrCreate([
            "name" => "Supervisor",
            "guard_name" => "web"
        ]);
        $shipping_supervisor->syncPermissions([
            "View_All_Custom_Shippings",
            "Asign_Custom_Shipping",
            "Update_Custom_Shipping",
            "View_Hs_Sheet",
            "Change_Custom_Shipping_Status",
            "View_Related_Worker_Dues",
            "View_Related_Advances",
            "Update_Shipping_Invoice",
        ]);

        $shipping_borker = Role::firstOrCreate([
            "name" => "Broker",
            "guard_name" => "web"
        ]);
        $shipping_borker->syncPermissions([
            "View_Related_Custom_Shippings",
            "View_Hs_Sheet",
            "Change_Custom_Shipping_Status",
            "View_Related_Worker_Dues",
            "View_Related_Advances",
            "Update_Shipping_Invoice",
            "View_Shipping_Invoices"
        ]);

        $shipping_borker = Role::firstOrCreate([
            "name" => "System Employee",
            "guard_name" => "web"
        ]);
        $shipping_borker->syncPermissions([
            "View_Hs_Sheet",
            "View Created By Custom Shippings",
            "Assign Custom Shipping To Broker",
            "Assign Custom Shipping To Client",
            "Upload Attachment In Custom Shipping",
            "Create_Custom_Shipping",
            "Update_Custom_Shipping",
        ]);
        $client = Role::firstOrCreate([
            "name" => "Client",
            "guard_name" => "web"
        ]);
        $client->syncPermissions([
            "View_Hs_Sheet",
            "View_Related_Custom_Shippings",
            "View_Related_Advances",
            "View_Shipping_Invoices",
            "View_Related_Client_Debits",
            "Create_Advance",
            "View_Advances",
            "View_Related_Advances",
            "Delete_Advance",
            "Update_Advance",
        ]);
        $subClient = Role::firstOrCreate([
            "name" => "SubClient",
            "guard_name" => "web"
        ]);
        $subClient->syncPermissions([
            "View_Hs_Sheet",
            "View_Related_Custom_Shippings",
            "View_Related_Advances",
            "View_Shipping_Invoices",
            "View_Related_Client_Debits",
        ]);
    }
}
