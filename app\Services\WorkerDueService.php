<?php

namespace App\Services;

use Exception;
use App\Models\Advance;
use App\Models\WorkerDue;
use Illuminate\Support\Str;
use App\Constants\WorkerDueAction;
use Illuminate\Support\Facades\DB;
use App\Events\NewExpenseAddedEvent;
use App\Constants\AttachmentVisibleStatus;
use App\Models\Wallet;

class WorkerDueService
{
    public function updateWorkerDue(array $data, $id)
    {
        try {
            DB::beginTransaction();
            $worker_wage = WorkerDue::findOrFail($id);
            $wallet = Wallet::where(['user_id' => $worker_wage->user_id])->first();
            if ($data["action_type"] == WorkerDueAction::Update->value) {
                $worker_wage->amount = $data["amount"];
            } else if ($data["action_type"] == WorkerDueAction::PartialPay->value) {
                $worker_wage->amount -= $data["amount"];
                $wallet->amount -= $data["amount"];
                $data["to_user_id"] = $worker_wage->user_id;
                $data["current_balance"] = $wallet?->amount;
                $this->createAdvance($data);
            } else if ($data["action_type"] == WorkerDueAction::FullPay->value) {
                $data["to_user_id"] = $worker_wage->user_id;
                $data["amount"] = $worker_wage->amount;
                $wallet->amount -= $worker_wage->amount;
                $data["current_balance"] =  $wallet->amount;
                $this->createAdvance($data);
                $worker_wage->amount = 0;
            }
            $worker_wage->save();
            $wallet->save();
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    private function createAdvance($data)
    {
        $advance = Advance::create([
            "to_user_id" => @$data["to_user_id"],
            "from_user_id" => @$data["from_user_id"],
            "amount" => $data["amount"],
            "date" => @$data["date"],
            "notes" => @$data["notes"],
            "status" => @$data["status"],
            "current_balance" => $data["current_balance"] ?? null,
            "created_by" => auth("web")->id()
        ]);
        $this->createNotificationToWorkerAfterAdvancePaid($advance);
        if (isset($data["attachments"])) {
            foreach ($data["attachments"] as $attachment) {
                $original_fielname = $attachment->getClientOriginalName();
                $mime_type = $attachment->getClientOriginalExtension();
                $uniqueName   = strtolower(Str::uuid() . '.' . $mime_type);

                $path         = "uploads/advances";
                $uploadPath   = public_path($path);
                $attachment->move($uploadPath, $uniqueName);
                $file_path = $path . "/" . $uniqueName;

                $advance->attachments()->create([
                    "file_path" => $file_path,
                    "filename" => $uniqueName,
                    "original_filename" => $original_fielname,
                    "mime_type" => $mime_type,
                    "visible_to" => AttachmentVisibleStatus::Employee
                ]);
            }
        }
        NewExpenseAddedEvent::dispatch($advance->amount);
    }
    private function __createAdvance($to_user, $amount)
    {
        $advance =   Advance::create([
            "to_user_id" => $to_user,
            "amount" => $amount,
            "created_by" => auth("web")->id()
        ]);
        $this->createNotificationToWorkerAfterAdvancePaid($advance);
    }
    public function createNotificationToWorkerAfterAdvancePaid($advance)
    {
        $params = [
            "title" => __("The Admin Has Gaven an Advance To you"),
            "content" => __("The Admin Has Gaven an Advance To you of total") . " " . $advance->amount,
            "to" => [
                $advance->to_user_id,
            ],
            "from_user_id" => auth("web")->id(),
            "route" => "admin.advances.index",
            "routeParams" => null,
            "reference_id" => $advance->id,
            "reference_type" => get_class($advance)
        ];
        (new NotificationsService())->createNotification($params);
    }
}
