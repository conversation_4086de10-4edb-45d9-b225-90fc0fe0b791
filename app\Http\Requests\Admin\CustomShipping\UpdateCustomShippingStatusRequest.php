<?php

namespace App\Http\Requests\Admin\CustomShipping;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateCustomShippingStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "status" => "required",
            "packages_count" => "required",
            "description" => "nullable",
            "invoice_date" => "nullable|date",
            "attachment" => "nullable|file|mimes:pdf,csv,xlxx,xlsx,doc,png,jpeg,jpg"
        ];
    }


    public function messages(): array
    {
        return [
            "status.required" => __("status is required"),
            "packages_count.required" => __("packages count is required"),
        ];
    }
}
