<?php

namespace App\Http\Controllers\Admin;

use Exception;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Client;
use App\Models\Document;
use Illuminate\Support\Str;
use App\Models\DocumentType;
use App\Models\Notification;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Services\DocumentService;
use App\Http\Controllers\Controller;
use App\Services\PdfService;
use App\Http\Requests\Admin\Document\CreateRequest;
use App\Http\Requests\Admin\Document\UpdateRequest;

class DocumentController extends Controller
{
    protected $crudService;
    public function __construct()
    {
        $this->crudService = new DocumentService();
        $this->middleware('is_able:Create Document')->only(['create', 'store']);
        $this->middleware('is_able:View Documents')->only(['index', 'view']);
        $this->middleware('is_able:Update Document')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Document')->only(['destroy']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Documents");
        return view("admin.documents.index", $data);
    }
    public function documentTypeList(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = DocumentType::withCount("documents")
                ->when(is_broker() || is_client(), function ($query) {
                    $query->whereHas("users", function ($inner) {
                        $inner->where("users.id", auth("web")->id());
                    });
                })
                ->when(is_sub_client(), function ($query) {
                    $query->whereHas("users", function ($inner) {
                        $client_id =  get_client_id();
                        $client = Client::findOrFail($client_id);
                        $inner->where("users.id", $client->user_id);
                    });
                })
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->where("name_ar", "LIKE", "%" . $search . "%")
                            ->orWhere("name_en", "LIKE", "%" . $search . "%");
                    });
                });

            if ($orderBy == "document_type") {
                $query = $query
                    ->leftJoin('document_types AS p', 'p.id', '=', 'documents.document_type_id')
                    ->orderBy('p.name', $direction)->select("documents.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.documents.document_types", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    public function documentTypes($id)
    {
        $document_type = DocumentType::findOrFail($id);
        $data["title"] = __("Documents In ") . $document_type->name;
        $data["document_type_id"] = $id;
        return view("admin.documents.types", $data);
    }


    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";
            $document_type_id = $request->document_type_id;
            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            $query = Document::with("documentType")
                ->when($document_type_id, fn($query) => $query->where("document_type_id", $document_type_id))
                ->when(is_broker() || is_client(), function ($query) {
                    $query->whereHas("documentType.users", function ($inner) {
                        $inner->where("users.id", auth("web")->id());
                    });
                })
                ->when(is_sub_client(), function ($query) {
                    $query->whereHas("documentType.users", function ($inner) {
                        $client_id =  get_client_id();
                        $client = Client::findOrFail($client_id);
                        $inner->where("users.id", $client->user_id);
                    });
                })
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->where("name_ar", "LIKE", "%" . $search . "%")
                            ->orWhere("name_en", "LIKE", "%" . $search . "%");
                    });
                });

            if ($orderBy == "document_type") {
                $query = $query
                    ->leftJoin('document_types AS p', 'p.id', '=', 'documents.document_type_id')
                    ->orderBy('p.name', $direction)->select("documents.*");
            } else {
                $query = $query
                    ->orderBy($orderBy, $direction);
            }
            $TotalCount = $query->count();
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.documents.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Documents");
        $data["sub_title"] = __("Add New Document");
        $data["document_types"] = DocumentType::where("status", 1)->get();
        return view('admin.documents.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateRequest $request)
    {
        try {
            $this->crudService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $doc = Document::findOrFail($id);
            return response()->json(['success' => true, 'data' => $doc]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("Documents");
        $data["sub_title"] = __("Update Document");
        $data["document_types"] = DocumentType::where("status", 1)->get();
        $data["document"] = Document::with("attachments")->findOrFail($id);
        return view('admin.documents.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, $id)
    {
        try {
            $this->crudService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            Document::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
    public function exportAllToPdf()
    {
        $logo = "";
        $document_type_id = request()->query("document_type_id");
        $documents = Document::query()->with("documentType")
            ->when($document_type_id, fn($query) => $query->where("document_type_id", $document_type_id))
            ->when(is_broker() || is_client(), function ($query) {
                $query->whereHas("documentType.users", function ($inner) {
                    $inner->where("users.id", auth("web")->id());
                });
            })->when(is_sub_client(), function ($query) {
                $query->whereHas("documentType.users", function ($inner) {
                    $client_id =  get_client_id();
                    $client = Client::findOrFail($client_id);
                    $inner->where("users.id", $client->user_id);
                });
            })
            ->get();
        $pdfService = new PdfService([
            'orientation' => 'landscape',
        ]);

        $pdf = $pdfService->generatePdf('admin.documents.export_pdf', [
            'documents' => $documents,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_documents.pdf", 'D');
    }
}
