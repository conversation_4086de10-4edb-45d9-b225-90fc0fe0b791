<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use App\Models\Client;
use App\Models\Setting;
use App\Constants\UserType;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Constants\CrudMessage;
use App\Models\CustomShipping;
use App\Constants\DeliveryMethod;
use App\Models\ShippingExpenseType;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Constants\CustomShippingType;
use App\Exports\CustomShippingExport;
use App\Constants\CustomShippingStatus;
use App\Services\CustomShippingService;
use App\Http\Requests\Admin\CustomShipping\CreateCustomShippingRequest;
use App\Http\Requests\Admin\CustomShipping\UpdateCustomShippingRequest;
use App\Models\Country;
use App\Models\ShippingExpense;
use App\Services\PdfService;
use App\Models\ShippingInvoice;

class CustomShippingController extends Controller
{

    public function __construct(protected CustomShippingService $customShippingService)
    {
        $this->middleware('is_able:Create Custom Shipping')->only(['create', 'store']);
        // $this->middleware('is_able:View_Related_Custom_Shippings')->only(['index', 'view']);
        $this->middleware('is_able_any:Update Custom Shippings,Update Related Custom Shippings,Update Created By Custom Shippings')->only(['edit', 'update']);
        $this->middleware('is_able:Delete Custom Shippings,Delete Related Custom Shippings,Delete Created By Custom Shippings')->only(['destroy']);
    }
    public function index()
    {
        $data["title"] = __("Custom Shippings");
        $data["statuses"] = CustomShippingStatus::availableTypes();
        $data["status_id"] = request()->query("status");
        return view("admin.custom_shippings.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $status_id = $request->Status_id;
            $from_date = $request->From_date;
            $to_date = $request->To_date;
            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;

            ## removed from query
            // if ($user->type == UserType::Broker && $user->can('View Related Custom Shippings')) {
            //     $query->where("assigned_to", $user->id);
            // } else if ($user->type == UserType::SystemEmployee && $user->can('View Created By Custom Shippings')) {
            //     $query->where("created_by", $user->id);
            // } else if ($user->type == UserType::Client && $user->can('View Related Custom Shippings')) {
            //     $query->where("client_id", $user->client->id);
            // } else if ($user->type == UserType::SubClient && $user->can('View Related Custom Shippings')) {
            //     $query->where("client_id", $user->client_id);
            // }

            $user = User::with("client")->find(auth("web")->id());
            $query = CustomShipping::with("client", "assignedTo")
                ->when($user, function ($query) use ($user) {
                    if ($user->can('View Related Custom Shippings') && $user->can('View Created By Custom Shippings')) {
                        $query->where(function ($sub) use ($user) {
                            $sub->where("assigned_to", $user->id)
                                ->orWhere('created_by', $user->id)
                                ->orWhere(function ($q) use ($user) {
                                    $q->where('client_id', '!=', null)
                                        ->where('client_id', $user->client?->id ?? $user->client_id);
                                });
                        });
                    } else if ($user->can('View Related Custom Shippings')) {
                        $query->where("assigned_to", $user->id)
                            ->orWhere(function ($q) use ($user) {
                                $q->whereNotNull('client_id')
                                    ->where('client_id', $user->client?->id ?? $user->client_id);
                            });
                    } else if ($user->can('View Created By Custom Shippings')) {
                        $query->Where('created_by', $user->id);
                    }
                })
                ->when($search, function ($query) use ($search) {
                    $query->where("policy_number", "LIKE", "%" . $search . "%");
                })->when(
                    $status_id != null && $status_id != CustomShippingStatus::UnderClearance->value,
                    function ($query) use ($status_id) {
                        $query->where("status", "=", $status_id);
                    }
                )->when($status_id == CustomShippingStatus::UnderClearance->value, function ($query) {
                    $query->whereNotIn("status", [
                        CustomShippingStatus::Distributed->value,
                        CustomShippingStatus::Expo_Exported->value,
                        CustomShippingStatus::RELEASED->value
                    ]);
                })->when($from_date, function ($query) use ($from_date) {
                    $query->whereDate("created_at", ">=", $from_date);
                })->when($to_date, function ($query) use ($to_date) {
                    $query->whereDate("created_at", "<=", $to_date);
                });
            $TotalCount = $query->count();

            // 
            if ($orderBy == "user_name") {
                $query = $query
                    ->leftJoin('users as u', 'u.id', '=', 'custom_shippings.assigned_to')
                    ->orderBy("u.name", $direction)->select("custom_shippings.*");
            } else if ($orderBy == "from_country") {
                $query = $query
                    ->leftJoin('countries as c', 'c.id', '=', 'custom_shippings.from_country_id')
                    ->orderBy(app()->getLocale() == "ar" ? "c.name_ar" : "c.name_en", $direction)->select("custom_shippings.*");
            } else if ($orderBy == "company_name") {
                $query = $query
                    ->leftJoin('companies as c', 'c.id', '=', 'custom_shippings.company_id')
                    ->orderBy(app()->getLocale() == "ar" ? "c.name" : "c.name", $direction)->select("custom_shippings.*");
            } else if ($orderBy == "client_name") {
                $query = $query
                    ->leftJoin('clients as c', 'c.id', '=', 'custom_shippings.client_id')
                    ->orderBy("c.name", $direction)->select("custom_shippings.*");
            } else {
                $query = $query->orderBy($orderBy, $direction);
            }
            $roles = $query
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.custom_shippings.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data["title"] = __("Custom Shippings");
        $data["sub_title"] = __("Add New Shipping");
        $data["users"] = User::where("type", UserType::Broker->value)->where('IsSuperAdmin', "!=", 1)->get(["id", "name_ar", "name_en"]);
        $data["clients"] = Client::query()->get(["id", "name_ar", "name_en"]);
        $data["types"] = CustomShippingType::availableTypes();
        $data["shipping_types"] = CustomShippingType::availableTypes2();
        $data["delivery_methods"] = DeliveryMethod::availableTypes();
        $country = Country::with("airports", "harbors")->where("is_default", 1)->first();
        $data["default_country"] = $country;
        $data["countries"] = Country::all(['id', "name_ar", "name_en"]);
        return view('admin.custom_shippings.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateCustomShippingRequest $request)
    {
        try {
            $this->customShippingService->store($request->validated());
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            // return $ex;
            session()->flash("error", $ex->getMessage());
            return back();
        }
    }

    public function search(Request $request)
    {
        try {
            $policy_number = $request->policy_number;
            $code = $request->code;

            if (!isset($code) && !isset($policy_number)) {
                session()->flash("error", __("Add Policy Number Or Shipping Code"));
                return back();
            }

            $custom_shipping = CustomShipping::when($policy_number, function ($query) use ($policy_number) {
                $query->where("policy_number", $policy_number);
            })->when($code, function ($query) use ($code) {
                $query->where("code", $code);
            })->firstOrFail();

            // add validation

            $user = auth("web")->user();
            $user = $user->load('client');
            if ($user->type == UserType::Broker || $user->type == UserType::SystemEmployee) {
                if (!$user->can("View All Custom Shippings")) {
                    if ($custom_shipping->created_by != $user->id && $custom_shipping->assigned_to != $user->id) {
                        session()->flash("error", __("Not Allowed"));
                        return back();
                    }
                }
            }
            if ($user->type == UserType::Client || $user->type == UserType::SubClient) {
                if (!$user->can("View All Custom Shippings")) {
                    if (isset($custom_shipping->client_id) && $custom_shipping->client_id != $user->client_id && $custom_shipping->client_id != $user->client?->id) {
                        session()->flash("error", __("Not Allowed"));
                        return back();
                    }
                }
            }

            return redirect()->route("admin.custom_shippings.show", $custom_shipping->id);
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return back();
        }
    }
    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {

            $data["title"] = __("Shipping status");
            $data["sub_title"] = __("Update Shipping status");
            $data["sub_title_class"] = "text-lg font-semibold text-blue-600 yahya"; // 👈 Add your custom class here

            $shipping = CustomShipping::findOrFail($id);
            $shipping = $shipping->load("shippingStatus.attachment", "assignedTo", "attachments", "shippingInvoice.shippingExpenses", "airport", "harbor");
            $data["shipping"] = $shipping;

            //Guard
            // if (auth("web")->user()->can("View Related Custom Shippings") && $shipping->assigned_to != auth("web")->id()) {
            //     abort(401);
            // }
            // //Gurad
            // $user = User::with("client")->find(auth('web')->id());
            // if (is_client() && auth("web")->user()->can("View Related Custom Shippings") && $shipping->client_id != $user->client->id) {
            //     abort(401);
            // }
            // if (is_sub_client() && auth("web")->user()->can("View Related Custom Shippings") && $shipping->client_id != $user->client_id) {
            //     abort(401);
            // }
            $user = auth("web")->user();
            $user = $user->load('client');
            if ($user->type == UserType::Broker || $user->type == UserType::SystemEmployee) {
                if (!$user->can("View All Custom Shippings")) {
                    if ($shipping->created_by != $user->id && $shipping->assigned_to != $user->id) {
                        session()->flash("error", __("Not Allowed"));
                        return back();
                    }
                }
            }
            if ($user->type == UserType::Client || $user->type == UserType::SubClient) {
                if (!$user->can("View All Custom Shippings")) {
                    if (isset($custom_shipping->client_id) && $custom_shipping->client_id != $user->client_id && $custom_shipping->client_id != $user->client?->id) {
                        session()->flash("error", __("Not Allowed"));
                        return back();
                    }
                }
            }

            $data["expense_types"] = ShippingExpenseType::all();
            return view('admin.custom_shippings.shipping_status', $data);
        } catch (Exception $ex) {
            session()->flash("error", __(CrudMessage::SomethingWentWrong));
            return back();
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data["title"] = __("Custom Shippings");
        $data["sub_title"] = __("Update Shipping");
        $data["users"] = User::where("type", UserType::Broker->value)->where('IsSuperAdmin', "!=", 1)->get();
        $shipping = CustomShipping::findOrFail($id);

        // Guard
        $user = auth("web")->user();
        $user = $user->load('client');
        if ($user->type == UserType::Broker || $user->type == UserType::SystemEmployee) {
            if (!$user->can("View All Custom Shippings")) {
                if ($shipping->created_by != $user->id && $shipping->assigned_to != $user->id) {
                    session()->flash("error", __("Not Allowed"));
                    return back();
                }
            }
        }
        if ($user->type == UserType::Client || $user->type == UserType::SubClient) {
            if (!$user->can("View All Custom Shippings")) {
                if (isset($custom_shipping->client_id) && $custom_shipping->client_id != $user->client_id && $custom_shipping->client_id != $user->client?->id) {
                    session()->flash("error", __("Not Allowed"));
                    return back();
                }
            }
        }
        $shipping = $shipping->load("attachments");
        $data["clients"] = Client::query()->get();
        $data["shipping"] = $shipping;
        $data["types"] = CustomShippingType::availableTypes();
        $data["shipping_types"] = CustomShippingType::availableTypes2();
        $data["delivery_methods"] = DeliveryMethod::availableTypes();
        $country = Country::with("airports", "harbors")->where("is_default", 1)->first();
        $data["country"] = $country;
        $data["countries"] = Country::all(['id', "name_ar", "name_en"]);
        return view('admin.custom_shippings.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCustomShippingRequest $request, string $id)
    {
        try {
            $this->customShippingService->update($request->validated(), $id);
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            // return $ex;
            session()->flash("error", $ex->getMessage());
            return back();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            // delete invoices and reduce balances
            $this->customShippingService->destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }

    public function exportAllToPdf()
    {
        $logo = Setting::where('key', 'logo')->first();

        $user = User::with("client")->find(auth("web")->id());
        $shippings = CustomShipping::with("assignedTo", "fromCountry")
            ->when($user, function ($query) use ($user) {
                if ($user->can('View Related Custom Shippings') && $user->can('View Created By Custom Shippings')) {
                    $query->where(function ($sub) use ($user) {
                        $sub->where("assigned_to", $user->id)
                            ->orWhere('created_by', $user->id)
                            ->orWhere(function ($q) use ($user) {
                                $q->where('client_id', '!=', null)
                                    ->where('client_id', $user->client?->id ?? $user->client_id);
                            });
                    });
                } else if ($user->can('View Related Custom Shippings')) {
                    $query->where("assigned_to", $user->id)
                        ->orWhere(function ($q) use ($user) {
                            $q->whereNotNull('client_id')
                                ->where('client_id', $user->client?->id ?? $user->client_id);
                        });
                } else if ($user->can('View Created By Custom Shippings')) {
                    $query->Where('created_by', $user->id);
                }
            })
            // ->when($user, function ($query) use ($user) {
            //     if ($user->type == UserType::Broker && $user->can('View Related Custom Shippings')) {
            //         $query->where("assigned_to", $user->id);
            //     } else if ($user->type == UserType::SystemEmployee && $user->can('View Created By Custom Shippings')) {
            //         $query->where("created_by", $user->id);
            //     } else if ($user->type == UserType::Client && $user->can('View Related Custom Shippings')) {
            //         $query->where("client_id", $user->client->id);
            //     } else if ($user->type == UserType::SubClient && $user->can('View Related Custom Shippings')) {
            //         $query->where("client_id", $user->client_id);
            //     }
            // })
            ->get();

        // استخدام PdfService مع الإعدادات المحسنة
        $pdfService = new PdfService([
            'orientation' => 'landscape',
            'margin_left' => 10,
            'margin_right' => 10,
        ]);

        $pdf = $pdfService->generatePdf('admin.custom_shippings.export_pdf', [
            'shippings' => $shippings,
            'logo' => $logo,
            'title' => __('Shippings Report')
        ]);

        $pdf->Output(time() . "_shippings.pdf", 'D');
    }

    public function printShipping($id)
    {
        $logo = Setting::where('key', 'logo')->first();
        $user = User::with("client")->find(auth("web")->id());
        $shipping = CustomShipping::with("assignedTo")->where('id', $id)->first();

        if ($user->type == UserType::Broker && !$user->IsSuperAdmin && $shipping->assigned_to != $user->id && !$user->can('View All Custom Shippings')) {
            return back();
        } else if ((is_client() || is_sub_client()) && $user->can('View Related Custom Shippings') &&  $user->client->id != $shipping->client_id) {
            return back();
        } elseif ($user->type == UserType::SystemEmployee && !$user->IsSuperAdmin && $shipping->created_by != $user->id && !$user->can('View All Custom Shippings')) {
            return back();
        }

        $pdfService = new PdfService([
            'orientation' => 'landscape',
            'margin_left' => 10,
            'margin_right' => 10,
        ]);

        $pdf = $pdfService->generatePdf('admin.custom_shippings.single_export_pdf', [
            'shipping' => $shipping,
            'logo' => $logo
        ]);

        $pdf->Output(time() . "_shipping.pdf", 'D');
    }
    public function exportAllToExcel()
    {
        return Excel::download(new CustomShippingExport(), 'shippings.xlsx');
    }

    public function getShippingsOfClient($user_id)
    {
        try {
            $user = User::with("client")->find($user_id);
            $item = CustomShipping::where("client_id", $user->client->id)->get(["id", "policy_number", "packages_count"]);
            return response()->json(['success' => true, 'data' => $item]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    public function getShippingExpensesOfShipment($custom_shipping_id)
    {
        try {
            $item = CustomShipping::findOrFail($custom_shipping_id);
            if (isset($item->employee_invoice_id)) {
                $shipping_expenses = ShippingExpense::where('shipping_invoice_id', $item->employee_invoice_id)->get();

                $invoice = ShippingInvoice::with("attachments")->findOrFail($item->employee_invoice_id);

                $data["attachments"] = $invoice->attachments;


                return response()->json(['success' => true, 'data' => $shipping_expenses]);
            }
            return response()->json(['success' => true, 'data' => []]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    public function loadAttachmentFromEmployeeInvoice($custom_shipping_id)
    {
        try {
            $item = CustomShipping::findOrFail($custom_shipping_id);
            $data["attachments"] = [];
            $data["users"] = [];
            if (isset($item->employee_invoice_id)) {
                $invoice = ShippingInvoice::with("attachments")->findOrFail($item->employee_invoice_id);
                $data["attachments"] = $invoice->attachments;
                $client = Client::findOrFail($item->client_id);
                $data["users"] = User::where('client_id', $client->id)
                    ->orWhere("id", $client->user_id)
                    ->get();
            }
            $view = view("admin.shipping_invoices.partials.attachments", $data)->render();
            return response($view);
        } catch (Exception $ex) {
            throw $ex;
        }
    }
}
