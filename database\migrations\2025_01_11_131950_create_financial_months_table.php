<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_months', function (Blueprint $table) {
            $table->id();
            $table->string("name")->nullable();
            $table->date("start_from")->nullable();
            $table->date("end_to")->nullable();
            $table->integer("status")->nullable();
            $table->unsignedBigInteger('financial_year_id')->index()->nullable();
            $table->foreign('financial_year_id')->references('id')->on('financial_years')->onUpdate('cascade')->onDelete('cascade');

            $table->decimal("start_balance", 10, 2)->nullable()->default(0.00);
            $table->decimal("end_balance", 10, 2)->nullable()->default(0.00);

            $table->decimal("total_expenses", 10, 2)->nullable()->default(0.00);
            $table->decimal("total_revenues", 10, 2)->nullable()->default(0.00);
            $table->decimal("withdrawn_revenues", 10, 2)->nullable()->default(0.00);

            $table->unsignedBigInteger("created_by")->nullable();
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_months');
    }
};
