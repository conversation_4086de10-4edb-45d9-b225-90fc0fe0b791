<?php

namespace App\Http\Requests\Admin\Document;

use App\Constants\CrudMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "name_ar" => "required|string",
            "name_en" => "required|string",
            "document_type_id" => "required|integer",
            "end_date" => "nullable|date",
            "notify_before" => "nullable|integer",
            "description" => "nullable",
            "created_by" => "sometimes",
            "attachments" => "required|array",
            "attachments.*" => "required|file|mimes:pdf,doc,docx,png,jpeg,xlsx,csv",
        ];
    }


    public function messages(): array
    {
        return [
            "name_ar.required" => CrudMessage::isRequired(__("Arabic Name")),
            "name_en.required" => CrudMessage::isRequired(__("English Name")),
            "document_type_id.required" => CrudMessage::isRequired(__("Document Type")),
            "attachments.required" => CrudMessage::isRequired(__("Attachments")),
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'created_by' => auth("web")->id(),
        ]);
    }
}
