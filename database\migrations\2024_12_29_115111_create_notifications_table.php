<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->text("title")->nullable();
            $table->string("content")->nullable();
            $table->string("route")->nullable();
            $table->json("routeParams")->nullable();
            $table->foreignId("user_id")->constrained("users")->cascadeOnDelete();
            $table->foreignId("from_user_id")->constrained("users")->cascadeOnDelete();
            $table->dateTime("read_at")->nullable();
            $table->unsignedBigInteger("reference_id")->nullable();
            $table->string("reference_type")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
