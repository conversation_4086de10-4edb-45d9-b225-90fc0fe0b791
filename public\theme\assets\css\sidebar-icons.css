/* Sidebar Icons Styling to Match Dashboard Cards */

.menu-vertical .menu-item .menu-link {
    display: flex;
    gap: 10px;
}

.menu-icon-wrapper {
    display: flex;
    align-items: center;
}

.menu-icon-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-icon-circle i {
    font-size: 1.2rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.menu-item.active .menu-icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-icon-circle {
        width: 30px;
        height: 30px;
    }
    
    .menu-icon-circle i {
        font-size: 1rem;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .menu-icon-circle {
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }
    
    .menu-item:hover .menu-icon-circle {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }
}

/* Smooth transitions for all menu elements */
.menu-link {
    transition: all 0.3s ease;
}

.menu-item .menu-link:hover {
    background-color: rgba(115, 103, 240, 0.08);
    border-radius: 8px;
}

/* Active state styling */
.menu-item.active .menu-link {
    background-color: rgba(115, 103, 240, 0.12);
    border-radius: 8px;
}

/* Sub-menu icon adjustments */
.menu-sub .menu-item .menu-icon-circle {
    width: 25px;
    height: 25px;
}

.menu-sub .menu-item .menu-icon-circle i {
    font-size: 0.9rem;
}
