<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CrudMessage;
use Exception;
use App\Models\Country;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\AirPort;
use App\Models\Harbor;

class CountryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data["title"] = __("Countries");
        $data["countries"] = Country::all();
        return view("admin.intialization.countries.index", $data);
    }

    public function list(Request $request)
    {
        $data = [];
        try {
            $RowsPerPage = 10;
            $currentPage = $request->PageNo;
            $orderBy = $request->OrderBy ?? "id";
            $direction = $request->Direction ?? "desc";

            $search = null;

            if (isset($request->Search)) {
                $search = trim(Str::lower($request->Search));
            }
            $skip = ($request->PageNo - 1) * $RowsPerPage;
            $take = $RowsPerPage;


            $query = Country::when($search, function ($query) use ($search) {
                $query
                    ->where("name", "LIKE", "%" . $search . "%")
                    ->orWhere("name_ar", "LIKE", "%" . $search . "%")
                    ->orWhere("name_en", "LIKE", "%" . $search . "%")
                    ->orWhere("iso3", "LIKE", "%" . $search . "%");
            });
            $TotalCount = $query->count();
            $roles = $query
                ->orderBy($orderBy, $direction)
                ->skip($skip)
                ->take($take)
                ->get();
            $StartFrom = ($request->PageNo - 1) * $RowsPerPage;

            $data["_Items"] = $roles;
            $data["totalCount"] = $TotalCount;
            $data["totalPages"] = ceil($TotalCount / $RowsPerPage);
            $data["currentPage"] = $currentPage;
            $data["startFrom"] = $StartFrom;
            $data["endTo"] = (($currentPage - 1) * $RowsPerPage) + $RowsPerPage;
            $data["orderBy"] = $orderBy;
            $data["direction"] = $direction;

            $view = view("admin.intialization.countries.list", $data)->render();
            return response($view);
        } catch (Exception $e) {
            //LOG
            return $e;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => "required|unique:countries,name",
            "short_name" => "required|unique:countries,short_name"
        ]);
        try {
            $data = $request->only(['name', "short_name", "iso3", "number_code", "phone_code", "type", "is_default"]);
            $country = Country::create($data);
            if ($request->is_default == "1") {
                Country::where('id', "!=", $country->id)->update(['is_default' => false]);
            }
            session()->flash("success", __(CrudMessage::CreatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $country = Country::findOrFail($id);
            return response()->json(['success' => true, 'data' => $country]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    public function getAirports($id)
    {
        try {
            $airports = AirPort::where('country_id', $id)->get();
            return response()->json(['success' => true, 'data' => $airports]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }

    public function getHarbors($id)
    {
        try {
            $harbors = Harbor::where('country_id', $id)->get();
            return response()->json(['success' => true, 'data' => $harbors]);
        } catch (Exception $ex) {
            return response()->json(['success' => false, "error" => $ex->getMessage()]);
        }
    }
    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "name" => "required|unique:countries,name," . $id,
            "short_name" => "required|unique:countries,short_name," . $id
        ]);
        try {
            $data = $request->only(['name', "short_name", "iso3", "number_code", "phone_code", "type", "is_default"]);
            $country = Country::findOrFail($id);
            $country->update($data);
            if ($request->is_default == "1") {
                Country::where('id', "!=", $country->id)->update(['is_default' => false]);
            }
            session()->flash("success", __(CrudMessage::UpdatedSuccessfully));
            return redirect()->back();
        } catch (Exception $ex) {
            return $ex;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            Country::destroy($id);
            return "success";
        } catch (Exception $e) {
            return false;
        }
    }
}
