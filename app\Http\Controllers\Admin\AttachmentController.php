<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Attachment;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AttachmentController extends Controller
{
    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $item = Attachment::findOrFail($id);
            $item->delete();
            DB::commit();
            return "success";
        } catch (Exception $ex) {
            DB::rollBack();
            session()->flash("error", $ex->getMessage());
            return false;
        }
    }
}
