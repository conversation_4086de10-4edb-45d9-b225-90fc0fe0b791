<?php

namespace App\Listeners;

use App\Constants\FinancialMonthStatus;
use App\Events\NewExpenseAddedEvent;
use App\Models\FinancialMonth;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class AddExpensesToFinancialMonth
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewExpenseAddedEvent $event): void
    {
        $current_month = FinancialMonth::where('status', FinancialMonthStatus::Opened)->first();
        $current_month->total_expenses += $event->amount;
        $current_month->save();
    }
}
